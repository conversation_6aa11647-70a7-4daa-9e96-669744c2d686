const traditionalChinese = {
    name: "Traditional Chinese",
    native_name: "繁體中文",
    code: "zh-TW",
};

const zh_tw = {
    translation: {
        report_bug: "回報問題",
        import: "匯入",
        import_from: "匯入自",
        file: "檔案",
        new: "新增",
        new_window: "開新視窗",
        open: "開啟",
        save: "儲存",
        save_as: "另存新檔",
        save_as_template: "儲存為範本",
        template_saved: "範本已儲存！",
        rename: "重新命名",
        delete_diagram: "刪除圖表",
        are_you_sure_delete_diagram:
            "您確定要刪除此圖表嗎？此操作無法復原。",
        oops_smth_went_wrong: "糟糕！發生錯誤。",
        import_diagram: "匯入圖表",
        import_from_source: "從 SQL 匯入",
        export_as: "匯出為",
        export_source: "匯出 SQL",
        models: "模型",
        exit: "結束",
        edit: "編輯",
        undo: "復原",
        redo: "重做",
        clear: "清除",
        are_you_sure_clear:
            "您確定要清除圖表嗎？此操作無法復原。",
        cut: "剪下",
        copy: "複製",
        paste: "貼上",
        duplicate: "建立副本",
        delete: "刪除",
        copy_as_image: "複製為圖片",
        view: "檢視",
        header: "選單列",
        sidebar: "側邊欄",
        issues: "問題",
        presentation_mode: "簡報模式",
        strict_mode: "嚴謹模式",
        field_details: "欄位詳細資料",
        reset_view: "重設檢視",
        show_grid: "顯示格線",
        show_datatype: "顯示資料型別",
        show_cardinality: "顯示基數",
        theme: "佈景主題",
        light: "淺色",
        dark: "深色",
        zoom_in: "放大",
        zoom_out: "縮小",
        fullscreen: "全螢幕",
        settings: "設定",
        show_timeline: "顯示時間軸",
        autosave: "自動儲存",
        panning: "平移",
        show_debug_coordinates: "顯示除錯座標",
        transform: "變形",
        viewbox: "檢視框",
        cursor_coordinates: "游標座標",
        coordinate_space: "座標空間",
        coordinate_space_screen: "螢幕",
        coordinate_space_diagram: "圖表",
        table_width: "表格寬度",
        language: "語言",
        export_saved_data: "匯出儲存的資料",
        flush_storage: "清除儲存空間",
        are_you_sure_flush_storage:
            "您確定要清除儲存空間嗎？這將會永久刪除所有圖表和自訂範本。",
        storage_flushed: "儲存空間已清除",
        help: "說明",
        shortcuts: "快速鍵",
        ask_on_discord: "在 Discord 上詢問我們",
        feedback: "意見回饋",
        no_changes: "無變更",
        loading: "載入中...",
        last_saved: "上次儲存時間",
        saving: "儲存中...",
        failed_to_save: "儲存失敗",
        fit_window_reset: "符合視窗大小 / 重設",
        zoom: "縮放",
        add_table: "新增資料表",
        add_area: "新增區域",
        add_note: "新增註解",
        add_type: "新增型別",
        to_do: "待辦事項",
        tables: "資料表",
        relationships: "關聯",
        subject_areas: "主題區域",
        notes: "註解",
        types: "型別",
        search: "搜尋...",
        no_tables: "尚無資料表",
        no_tables_text: "開始建立您的圖表！",
        no_relationships: "尚無關聯",
        no_relationships_text: "拖曳以連接欄位並建立關聯！",
        no_subject_areas: "尚無主題區域",
        no_subject_areas_text: "將主題區域新增至群組資料表！",
        no_notes: "尚無註解",
        no_notes_text: "使用註解記錄額外資訊",
        no_types: "尚無型別",
        no_types_text: "建立您的自訂資料型別",
        no_issues: "未偵測到問題。",
        strict_mode_is_on_no_issues:
            "嚴謹模式已關閉，因此不會顯示任何問題。",
        name: "名稱",
        type: "型別",
        null: "允許空值",
        not_null: "不允許空值",
        primary: "主鍵",
        unique: "唯一值",
        autoincrement: "自動遞增",
        default_value: "預設值",
        check: "檢查條件",
        this_will_appear_as_is: "*這將以原樣出現在產出的指令碼中。",
        comment: "註解",
        add_field: "新增欄位",
        values: "值",
        size: "大小",
        precision: "精確度",
        set_precision: "設定精確度：'大小, 位數'",
        use_for_batch_input: "使用逗號(,)進行批次輸入",
        indices: "索引",
        add_index: "新增索引",
        select_fields: "選擇欄位",
        title: "標題",
        not_set: "未設定",
        foreign: "外部",
        cardinality: "基數",
        on_update: "更新時",
        on_delete: "刪除時",
        swap: "交換",
        one_to_one: "一對一",
        one_to_many: "一對多",
        many_to_one: "多對一",
        content: "內容",
        types_info:
            "此功能適用於 PostgreSQL 等物件關聯式 DBMS。\n若用於 MySQL 或 MariaDB，將產出具 JSON 驗證檢查的 JSON 型別。\n若用於 SQLite，將轉換為 BLOB。\n若用於 MSSQL，將產出指向第一個欄位的型別別名。",
        table_deleted: "資料表已刪除",
        area_deleted: "區域已刪除",
        note_deleted: "註解已刪除",
        relationship_deleted: "關聯已刪除",
        type_deleted: "型別已刪除",
        cannot_connect: "無法連接，欄位型別不符",
        copied_to_clipboard: "已複製到剪貼簿",
        create_new_diagram: "建立新圖表",
        cancel: "取消",
        open_diagram: "開啟圖表",
        rename_diagram: "重新命名圖表",
        export: "匯出",
        export_image: "匯出圖片",
        create: "建立",
        confirm: "確認",
        last_modified: "最後修改",
        drag_and_drop_files: "將檔案拖曳至此處或點選以上傳。",
        upload_sql_to_generate_diagrams:
            "上傳 SQL 檔案以自動產出資料表及欄位。",
        overwrite_existing_diagram: "覆蓋現有圖表",
        only_mysql_supported:
            "*目前僅支援載入 MySQL 指令碼。",
        blank: "空白",
        filename: "檔案名稱",
        table_w_no_name: "有未命名的資料表",
        duplicate_table_by_name: "資料表名稱「{{tableName}}」重複",
        empty_field_name: "資料表「{{tableName}}」中有未命名的欄位",
        empty_field_type: "資料表「{{tableName}}」中有未指定型別的欄位",
        no_values_for_field:
            "資料表「{{tableName}}」中的欄位「{{fieldName}}」型別為「{{type}}」但未指定任何值",
        default_doesnt_match_type:
            "資料表「{{tableName}}」中欄位「{{fieldName}}」的預設值與其型別不符",
        not_null_is_null:
            "資料表「{{tableName}}」中的欄位「{{fieldName}}」設定為不可為空值但預設為空值",
        duplicate_fields:
            "資料表「{{tableName}}」中欄位名稱「{{fieldName}}」重複",
        duplicate_index:
            "資料表「{{tableName}}」中索引名稱「{{indexName}}」重複",
        empty_index: "資料表「{{tableName}}」中有未包含任何欄位的索引",
        no_primary_key: "資料表「{{tableName}}」未設定主鍵",
        type_with_no_name: "有未命名的型別",
        duplicate_types: "型別名稱「{{typeName}}」重複",
        type_w_no_fields: "型別「{{typeName}}」未定義任何欄位",
        empty_type_field_name: "型別「{{typeName}}」中有未命名的欄位",
        empty_type_field_type: "型別「{{typeName}}」中有未指定型別的欄位",
        no_values_for_type_field:
            "型別「{{typeName}}」中的欄位「{{fieldName}}」型別為「{{type}}」但未指定任何值",
        duplicate_type_fields:
            "型別「{{typeName}}」中欄位名稱「{{fieldName}}」重複",
        duplicate_reference: "關聯名稱「{{refName}}」重複",
        circular_dependency: "資料表「{{refName}}」存在循環相依性問題",
        timeline: "時間軸",
        priority: "優先順序",
        none: "無",
        low: "低",
        medium: "中",
        high: "高",
        sort_by: "排序方式",
        my_order: "自訂排序",
        completed: "已完成",
        alphabetically: "依字母順序",
        add_task: "新增工作項目",
        details: "詳細資料",
        no_tasks: "目前尚無工作項目。",
        no_activity: "目前尚無任何活動記錄。",
        move_element: "將「{{name}}」移動至 {{coords}}",
        edit_area: "{{extra}} 編輯區域「{{areaName}}」",
        delete_area: "刪除區域「{{areaName}}」",
        edit_note: "{{extra}} 編輯註解「{{noteTitle}}」",
        delete_note: "刪除註解「{{noteTitle}}」",
        edit_table: "{{extra}} 編輯資料表「{{tableName}}」",
        delete_table: "刪除資料表「{{tableName}}」",
        edit_type: "{{extra}} 編輯型別「{{typeName}}」",
        delete_type: "刪除型別「{{typeName}}」",
        add_relationship: "新增關聯",
        edit_relationship: "{{extra}} 編輯關聯「{{refName}}」",
        delete_relationship: "刪除關聯「{{refName}}」",
        not_found: "找不到",
        pick_db: "選擇資料庫",
        generic: "通用",
        generic_description:
            "通用圖表可匯出至任何 SQL 格式，但僅支援少數資料型別。",
        enums: "列舉",
        add_enum: "新增列舉",
        edit_enum: "{{extra}} 編輯列舉「{{enumName}}」",
        delete_enum: "刪除列舉",
        enum_w_no_name: "未指定名稱的列舉",
        enum_w_no_values: "列舉「{{enumName}}」未定義任何值",
        duplicate_enums: "列舉名稱「{{enumName}}」重複",
        no_enums: "尚無列舉",
        no_enums_text: "在此定義列舉",
        declare_array: "宣告陣列",
        empty_index_name: "資料表「{{tableName}}」中有未命名的索引",
        didnt_find_diagram: "糟糕！找不到該圖表。",
        unsigned: "無號數",
        share: "分享",
        unshare: "取消分享",
        copy_link: "複製連結",
        readme: "說明文件",
        failed_to_load: "載入失敗。請確認連結是否正確。",
        share_info:
            "* 分享此連結不會建立即時的協作工作階段。",
        show_relationship_labels: "顯示關聯標籤",
        docs: "文件",
        supported_types: "支援的檔案類型：",
        bulk_update: "批次更新",
        multiselect: "多重選取",
    },
};

export {zh_tw, traditionalChinese};
