const armenian = {
    name: "Armenian",
    native_name: "Հայերեն",
    code: "hy",
};

const hy = {
    translation: {
        report_bug: "Զեկուցել սխալի մասին",
        import_from: "Ներմուծել",
        import: "Ներմուծել",
        file: "Ֆայլ",
        new: "Նոր",
        new_window: "Նոր պատուհան",
        open: "Բացել",
        save: "Պահպանել",
        save_as: "Պահպանել որպես",
        save_as_template: "Պահպանել որպես ձևանմուշ",
        template_saved: "Ձևանմուշը պահպանվաձ է",
        rename: "Վերանվանել",
        delete_diagram: "Ջնջել սխեման",
        are_you_sure_delete_diagram:
            "Վստա՞հ եք, որ ցանկանում եք ջնջել այս սխեման։ Այս գործողությունը անդառնալի է։",
        oops_smth_went_wrong: "Ուֆֆ, չստացվեց",
        import_diagram: "Ներմուծել սխեման",
        import_from_source: "Ներմուծել SQL-ից",
        export_as: "Արտահանել որպես",
        export_source: "Արտահանել SQL",
        models: "Մոդելներ",
        exit: "Ելք",
        edit: "Խմբագրել",
        undo: "Հետարկել",
        redo: "Կրկնել",
        clear: "Դատարկել",
        are_you_sure_clear:
            "Վստա՞հ եք, որ ցանկանում եք դատարկել սխեման։ Այս գործողությունը անդառնալի է։",
        cut: "Կտրել",
        copy: "Պատճենել",
        paste: "Տեղադրել",
        duplicate: "Կրկնօրինակել",
        delete: "Ջնջել",
        copy_as_image: "Պատճենել որպես նկար",
        view: "Տեսք",
        header: "Մենյուի գոտի",
        sidebar: "Կողմնագոտի",
        issues: "Խնդիրներ",
        presentation_mode: "Ներկայացման ռեժիմ",
        strict_mode: "Խիստ ռեժիմ",
        field_details: "Դաշտի մանրամասներ",
        reset_view: "Վերականգնել տեսքը",
        show_grid: "Ցուցադրել ցանցը",
        show_cardinality: "Ցուցադրել կարդինալությունը",
        theme: "Ֆոն",
        light: "Լուսավոր",
        dark: "Մութ",
        zoom_in: "Մեծացնել",
        zoom_out: "Փոքրացնել",
        fullscreen: "Լիաէկրան",
        settings: "Կարգավորումներ",
        show_timeline: "Պատմություն",
        autosave: "Ավտոմատ պահպանել",
        panning: "Շարժել սխեման",
        show_debug_coordinates: "Ցուցադրել կոորդինատները",
        transform: "Փոփոխություն",
        viewbox: "Դիտման գոտի",
        cursor_coordinates: "Կուրսորի կոորդինատներ",
        coordinate_space: "Տարածք",
        coordinate_space_screen: "Էկրան",
        coordinate_space_diagram: "Սխեմա",
        table_width: "Աղյուսակի լայնություն",
        language: "Լեզու",
        flush_storage: "Դատարկել պահեստը",
        are_you_sure_flush_storage:
            "Վստա՞հ եք, որ ցանկանում եք մաքրել պահոցը։ Սա անդառնալիորեն կհեռացնի ձեր բոլոր սխեմաներն ու ձևանմուշները։",
        storage_flushed: "Պահեստը դատարկված է",
        help: "Օգնություն",
        shortcuts: "Դյուրանցումներ",
        ask_on_discord: "Հարցնել Discord-ում",
        feedback: "Հետադարձ կապ",
        no_changes: "Փոփոխություններ չկան",
        loading: "Բեռնվում է...",
        last_saved: "Վերջին պահպանումը",
        saving: "Պահպանվում է...",
        failed_to_save: "Չհաջողվեց պահպանել",
        fit_window_reset: "Հարմարեցնել պատուհանին",
        zoom: "Մեծացնել",
        add_table: "Նոր աղյուսակ",
        add_area: "Ներ մակերես",
        add_note: "Նոր նշում",
        add_type: "Նոր տիպ",
        to_do: "Առաջադրանքներ",
        tables: "Աղյուսակներ",
        relationships: "Հարաբերություններ",
        subject_areas: "Կազմակերպման մակերեսներ",
        notes: "Նշումներ",
        types: "Տիպեր",
        search: "Փնտրել...",
        no_tables: "Աղյուսակներ չկան",
        no_tables_text: "Սկսեք կառուցել ձեր սխեման",
        no_relationships: "Հարաբերություններ չկան",
        no_relationships_text:
            "Քաշեք և միացրեք դաշտերը հարաբերություններ ստեղծելու համար",
        no_subject_areas: "Կազմակերպման մակերեսներ չկան",
        no_subject_areas_text:
            "Ավելացրեք Կազմակերպման մակերեսներ աղյուսակները խմբավորելու համար",
        no_notes: "Նշումներ չկան",
        no_notes_text:
            "Օգտագործեք նշումները լրացուցիչ տեղեկություններ գրանցելու համար",
        no_types: "Տիպեր չկան",
        no_types_text: "Ստեղծեք ձեր սեփական տվյալների տիպերը",
        no_issues: "Խնդիրներ չկան",
        strict_mode_is_on_no_issues:
            "Խիստ ռեժիմը միցված է, ուստի խնդիրներ չեն ցուցադրվի։",
        name: "Անուն",
        type: "Տիպ",
        null: "Null",
        not_null: "Not null",
        primary: "Primary",
        unique: "Unique",
        autoincrement: "Autoincrement",
        default_value: "Default",
        check: "Check արտահայտություն",
        this_will_appear_as_is: "*Սա կհայտնվի գեներացված կոդում հենց այսպես։",
        comment: "Մեկնաբանություն",
        add_field: "Նոր դաշտ",
        values: "Արժեքներ",
        size: "Չափ",
        precision: "Ճշտություն",
        set_precision: "Սահմանել ճշտություն՝ (չափ, թվանշաններ)",
        use_for_batch_input: "Օգտագործեք , խմբային ներմուծման համար",
        indices: "Ինդեքսներ",
        add_index: "Նոր ինդեքս",
        select_fields: "Ընտրել դաշտեր",
        title: "Վերնագիր",
        not_set: "Չսահմանված",
        foreign: "Foreign",
        cardinality: "Կարդինալություն",
        on_update: "Թարմացնելիս",
        on_delete: "Ջնջելիս",
        swap: "Փոխանակել",
        one_to_one: "Մեկից մեկ",
        one_to_many: "Մեկից շատ",
        many_to_one: "Շատից մեկ",
        content: "Բովանդակություն",
        types_info:
            "Այս ֆունկցիան նախատեսված է օբյեկտ-ռելյացիոն DBMS-ների համար, ինչպիսիք են PostgreSQL-ը։\nԵթե օգտագործվի MySQL կամ MariaDB համար, կգեներացվի JSON տեսակ համապատասխան json վավերացման ստուգումով։\nԵթե օգտագործվի SQLite-ի համար, այն կթարգմանվի BLOB-ի։\nԵթե օգտագործվի MSSQL-ի համար, կգեներացվի առաջին դաշտի տեսակալիաս։",
        table_deleted: "Աղյուսակը ջնջված է",
        area_deleted: "Մակերեսը ջհջված է",
        note_deleted: "Նմուշը ջհջված է",
        relationship_deleted: "Հարաբերությունը ջնջված է",
        type_deleted: "Տիպը ջհջված է",
        cannot_connect: "Չի հաջողվում միացնել, դաշտորը տարբեր տեսակներ ունեն",
        copied_to_clipboard: "Պատճենված է սեղմակապին",
        create_new_diagram: "Ստեղծել նոր սխեմա",
        cancel: "Չեղարկել",
        open_diagram: "Բացել սխեմա",
        rename_diagram: "Վերանվանել սխեման",
        export: "Արտահանել",
        export_image: "Արտահանել պատկերը",
        create: "Ստեղծել",
        confirm: "Հաստատել",
        last_modified: "Վերջին փոփոխությունը",
        drag_and_drop_files:
            "Քաշեք և թողեք ֆայլը այստեղ կամ սեղմեք բեռնելու համար։",
        upload_sql_to_generate_diagrams:
            "Վերբեռնեք sql ֆայլ՝ ձեր աղյուսակներն ու սյունակները ավտոմատ ստեղծելու համար։",
        overwrite_existing_diagram: "Փոխարինել առկա սխեման",
        only_mysql_supported: "",
        blank: "Դատարկ",
        filename: "Ֆայլի անուն",
        table_w_no_name: "Հայտարարվել է աղյուսակ առանց անվան",
        duplicate_table_by_name: "Կրկնօրինակված աղյուսակ '{{tableName}}' անվամբ",
        empty_field_name: "Դատարկ դաշտ 'name' '{{tableName}}' աղյուսակում",
        empty_field_type: "Դատարկ դաշտ 'type' '{{tableName}}' աղյուսակում",
        no_values_for_field:
            "'{{fieldName}}' դաշտը '{{tableName}}' աղյուսակում `{{type}}` տեսակի է, բայց արժեքներ չեն նշվել",
        default_doesnt_match_type:
            "Default արժեքը '{{tableName}}' աղյուսակի '{{fieldName}}' դաշտում չի համընկնում իր տեսակի հետ",
        not_null_is_null:
            "'{{fieldName}}' դաշտ '{{tableName}}' աղյուսակում NOT NULL է, բայց default NULL է",
        duplicate_fields:
            "Կրկնօրինակված դաշտեր '{{fieldName}}' անվամբ '{{tableName}}' աղյուսակում",
        duplicate_index:
            "Կրկնօրինակված ինդեքս '{{indexName}}' անվամբ '{{tableName}}' աղյուսակում",
        empty_index: "Ինդեքս '{{tableName}}' աղյուսակում չի ընդգրկում սյունակներ",
        no_primary_key: "'{{tableName}}' աղյուսակը չունի հիմնական բանալի",
        type_with_no_name: "Հայտարարվել է տիպ առանց անվան",
        duplicate_types: "Կրկնօրինակված տիպ '{{typeName}}' անվամբ",
        type_w_no_fields:
            "Հայտարարվել է դատարկ տիպ '{{typeName}}' անվամբ առանց դաշտերի",
        empty_type_field_name: "Դատարկ դաշտ 'name' '{{typeName}}' տիպում",
        empty_type_field_type: "Դատարկ դաշտ 'type' '{{typeName}}' տիպում",
        no_values_for_type_field:
            "'{{fieldName}}' դաշտ '{{typeName}}' տիպում 'type' տիպի է, բայց արժեքներ չեն նշվել",
        duplicate_type_fields:
            "Կրկնօրինակված դաշտեր '{{fieldName}}' անվամբ '{{typeName}}' տիպում",
        duplicate_reference: "Կրկնօրինակված հղում '{{refName}}' անվամբ",
        circular_dependency: "Շրջանաձև կախվածություն '{{refName}}' աղյուսակում",
        timeline: "Ժամանակացույց",
        priority: "Առաջնահերթություն",
        none: "Ոչ մի",
        low: "Ցածր",
        medium: "Միջին",
        high: "Բարձր",
        sort_by: "Տեսակավորել ըստ՝",
        my_order: "Իմ կարգ",
        completed: "Ավարտված",
        alphabetically: "Այբբենական",
        add_task: "Ավելացնել առաջադրանք",
        details: "Մանրամասներ",
        no_tasks: "Դուք դեռ չունեք առաջադրանքներ։",
        no_activity: "Դուք դեռ չունեք գործողություններ։",
        move_element: "Տեղափոխել {{name}} {{coords}}-ում",
        edit_area: "{{extra}} Խմբագրել մակերես {{areaName}}",
        delete_area: "Ջնջել մակերես {{areaName}}",
        edit_note: "{{extra}} Խմբագրել նշում {{noteTitle}}",
        delete_note: "Ջնջել նշում {{noteTitle}}",
        edit_table: "{{extra}} Խմբագրել աղյուսակ {{tableName}}",
        delete_table: "Ջնջել աղյուսակ {{tableName}}",
        edit_type: "{{extra}} Խմբագրել  {{typeName}}",
        delete_type: "Ջնջել  {{typeName}}",
        add_relationship: "Ավելացնել հարաբերություն",
        edit_relationship: "{{extra}} Խմբագրել հարաբերություն {{refName}}",
        delete_relationship: "Ջնջել հարաբերություն {{refName}}",
        not_found: "Չի գտնվել",
        pick_db: "Ընտրեք տվյալների բազան",
        generic: "Ընդհանուր",
        generic_description:
            "Ընդհանուր սխեմաները կարող են արտահանվել ցանկացած SQL տիպի, բայց աջակցում են քիչ տվյալների տեսակներ։",
        enums: "Enum-ներ",
        add_enum: "Ավելացնել enum",
        edit_enum: "{{extra}} Խմբագրել enum {{enumName}}",
        delete_enum: "Ջնջել enum",
        enum_w_no_name: "Հայտնաբերվել է enum առանց անվան",
        enum_w_no_values: "Հայտնաբերվել է enum '{{enumName}}' առանց արժեքների",
        duplicate_enums: "Կրկնօրինակված enum '{{enumName}}' անվամբ",
        no_enums: "Enum-ներ չկան",
        no_enums_text: "Սահմանեք enum-ները այստեղ",
        declare_array: "Հայտարարել array",
        empty_index_name:
            "Հայտարարվել է ինդեքս '{{tableName}}' աղյուսակում առանց անվան",
        didnt_find_diagram: "Ուֆֆ! Սխեման չի գտնվել։",
    },
};

export {hy, armenian};
