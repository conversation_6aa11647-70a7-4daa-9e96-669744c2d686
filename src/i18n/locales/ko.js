const korean = {
    name: "Korean",
    native_name: "한국어",
    code: "ko",
};

const ko = {
    translation: {
        report_bug: "문제 보고",
        import_from: "가져오기",
        import: "가져오기",
        file: "파일",
        new: "새로 만들기",
        new_window: "새 탭에서 열기",
        open: "열기",
        save: "저장",
        save_as: "다른 이름으로 저장",
        save_as_template: "템플릿으로 저장",
        template_saved: "템플릿이 저장되었습니다!",
        rename: "이름 변경",
        delete_diagram: "다이어그램 삭제",
        are_you_sure_delete_diagram:
            "이 다이어그램을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.",
        oops_smth_went_wrong: "이런! 문제가 발생했습니다.",
        import_diagram: "다이어그램 가져오기",
        import_from_source: "SQL에서 가져오기",
        export_as: "내보내기",
        export_source: "SQL로 내보내기",
        models: "모델",
        exit: "끝내기",
        edit: "편집",
        undo: "실행 취소",
        redo: "다시 실행",
        clear: "지우기",
        are_you_sure_clear:
            "이 다이어그램을 지우시겠습니까? 이 작업은 되돌릴 수 없습니다.",
        cut: "잘라내기",
        copy: "복사",
        paste: "붙여넣기",
        duplicate: "복제",
        delete: "삭제",
        copy_as_image: "이미지로 복사",
        view: "보기",
        header: "메뉴바",
        sidebar: "사이드바",
        issues: "이슈",
        presentation_mode: "발표 모드",
        strict_mode: "엄격 모드",
        field_details: "필드 세부 사항",
        reset_view: "보기 초기화",
        show_grid: "그리드 보기",
        show_cardinality: "관계 보기",
        theme: "테마",
        light: "라이트",
        dark: "다크",
        zoom_in: "확대",
        zoom_out: "축소",
        fullscreen: "전체 화면",
        settings: "설정",
        show_timeline: "타임라인 보기",
        autosave: "자동 저장",
        panning: "캔버스 드래그 이동",
        show_debug_coordinates: "디버그 좌표 보기",
        transform: "변환",
        viewbox: "뷰 박스",
        cursor_coordinates: "커서 좌표",
        coordinate_space: "좌표 공간",
        coordinate_space_screen: "화면",
        coordinate_space_diagram: "다이어그램",
        table_width: "테이블 너비",
        language: "언어",
        flush_storage: "저장소 비우기",
        are_you_sure_flush_storage:
            "저장소를 비우시겠습니까? 이 작업은 되돌릴 수 없으며 모든 다이어그램과 사용자 정의 템플릿이 삭제됩니다.",
        storage_flushed: "저장소가 비워졌습니다",
        help: "도움말",
        shortcuts: "단축키",
        ask_on_discord: "디스코드에서 문의하기",
        feedback: "피드백",
        no_changes: "변경 사항 없음",
        loading: "로딩 중...",
        last_saved: "마지막 저장",
        saving: "저장 중...",
        failed_to_save: "저장 실패",
        fit_window_reset: "창 크기에 맞추기/초기화",
        zoom: "화면 확대/축소",
        add_table: "테이블 추가",
        add_area: "영역 추가",
        add_note: "노트 추가",
        add_type: "유형 추가",
        to_do: "할 일",
        tables: "테이블",
        relationships: "관계",
        subject_areas: "주제 영역",
        notes: "노트",
        types: "유형",
        search: "검색...",
        no_tables: "테이블 없음",
        no_tables_text: "다이어그램을 만드세요!",
        no_relationships: "관계 없음",
        no_relationships_text: "필드를 연결하여 관계를 만드세요!",
        no_subject_areas: "주제 영역 없음",
        no_subject_areas_text: "테이블을 그룹화할 주제 영역을 추가하세요!",
        no_notes: "노트 없음",
        no_notes_text: "노트를 추가하여 추가 정보를 기록하세요",
        no_types: "유형 없음",
        no_types_text: "사용자 정의 데이터 유형을 만드세요",
        no_issues: "감지된 이슈가 없습니다.",
        strict_mode_is_on_no_issues:
            "엄격 모드가 꺼져 있어 이슈가 표시되지 않습니다.",
        name: "이름",
        type: "유형",
        null: "널",
        not_null: "널 아님",
        primary: "기본 키",
        unique: "고유",
        autoincrement: "자동 증가",
        default_value: "기본값",
        check: "검사 식",
        this_will_appear_as_is: "*이 내용은 생성된 스크립트에 그대로 표시됩니다.",
        comment: "주석",
        add_field: "필드 추가",
        values: "값",
        size: "크기",
        precision: "정밀도",
        set_precision: "정밀도 설정: (크기, 자릿수)",
        use_for_batch_input: "배치 입력을 위해서 ,(쉼표)를 사용하세요",
        indices: "인덱스",
        add_index: "인덱스 추가",
        select_fields: "필드 선택",
        title: "제목",
        not_set: "설정되지 않음",
        foreign: "외래 키",
        cardinality: "카디널리티",
        on_update: "업데이트 시",
        on_delete: "삭제 시",
        swap: "교환",
        one_to_one: "일 대 일",
        one_to_many: "일 대 다",
        many_to_one: "다 대 일",
        content: "내용",
        types_info:
            "이 기능은 PostgreSQL과 같은 객체 관계형 데이터베이스 관리 시스템에 적합합니다.\nMySQL 또는 MariaDB에서는 해당 JSON 검사를 포함한 JSON 유형으로 생성됩니다.\nSQLite에서는 BLOB으로 변환됩니다.\nMSSQL에서는 첫 번째 필드의 유형 별칭이 생성됩니다.",
        table_deleted: "테이블이 삭제되었습니다",
        area_deleted: "영역이 삭제되었습니다",
        note_deleted: "노트가 삭제되었습니다",
        relationship_deleted: "관계가 삭제되었습니다",
        type_deleted: "유형이 삭제되었습니다",
        cannot_connect: "연결할 수 없습니다, 열의 유형이 다릅니다",
        copied_to_clipboard: "클립보드에 복사되었습니다",
        create_new_diagram: "새 다이어그램 만들기",
        cancel: "취소",
        open_diagram: "다이어그램 열기",
        rename_diagram: "다이어그램 이름 변경",
        export: "내보내기",
        export_image: "이미지로 내보내기",
        create: "생성",
        confirm: "확인",
        last_modified: "마지막 수정",
        drag_and_drop_files: "파일을 여기에 끌어다 놓거나 클릭하여 업로드하세요.",
        upload_sql_to_generate_diagrams:
            "SQL 파일을 업로드하여 테이블과 열을 자동 생성하세요.",
        overwrite_existing_diagram: "기존 다이어그램 덮어쓰기",
        only_mysql_supported: "현재는 MySQL 스크립트만 지원합니다.",
        blank: "비어있음",
        filename: "파일 이름",
        table_w_no_name: "이름이 없는 테이블이 선언되었습니다",
        duplicate_table_by_name:
            "'{{tableName}}'라는 이름의 테이블이 중복 선언되었습니다",
        empty_field_name: "'{{tableName}}' 테이블의 필드 'name'이 비어 있습니다",
        empty_field_type: "'{{tableName}}' 테이블의 필드 'type'이 비어 있습니다",
        no_values_for_field:
            "'{{tableName}}' 테이블의 '{{fieldName}}' 필드 유형이 '{{type}}'이지만 값이 지정되지 않았습니다",
        default_doesnt_match_type:
            "'{{tableName}}' 테이블의 '{{fieldName}}' 필드 기본값이 유형과 일치하지 않습니다",
        not_null_is_null:
            "'{{tableName}}' 테이블의 '{{fieldName}}' 필드가 NOT NULL이지만 기본값이 NULL입니다",
        duplicate_fields:
            "'{{tableName}}' 테이블에서 '{{fieldName}}' 필드가 중복 선언되었습니다",
        duplicate_index:
            "'{{tableName}}' 테이블에서 '{{indexName}}' 인덱스가 중복 선언되었습니다",
        empty_index: "'{{tableName}}' 테이블의 인덱스에 열이 지정되지 않았습니다",
        no_primary_key: "'{{tableName}}' 테이블에 기본 키가 없습니다",
        type_with_no_name: "이름이 없는 유형이 선언되었습니다",
        duplicate_types: "'{{typeName}}'라는 이름의 유형이 중복 선언되었습니다",
        type_w_no_fields:
            "'{{typeName}}' 유형에 필드가 없는 빈 유형이 선언되었습니다",
        empty_type_field_name: "'{{typeName}}' 유형의 필드 'name'이 비어 있습니다",
        empty_type_field_type: "'{{typeName}}' 유형의 필드 'type'이 비어 있습니다",
        no_values_for_type_field:
            "'{{typeName}}' 유형의 '{{fieldName}}' 필드 유형이 '{{type}}'이지만 값이 지정되지 않았습니다",
        duplicate_type_fields:
            "'{{typeName}}' 사용자 정의 클래스에서 '{{fieldName}}' 필드가 중복 선언되었습니다",
        duplicate_reference: "'{{refName}}'라는 이름의 참조가 중복 선언되었습니다",
        circular_dependency: "'{{refName}}' 테이블을 포함한 순환 종속이 있습니다",
        timeline: "타임라인",
        priority: "우선 순위",
        none: "없음",
        low: "낮음",
        medium: "중간",
        high: "높음",
        sort_by: "정렬 기준",
        my_order: "내 정렬",
        completed: "완료됨",
        alphabetically: "알파벳 순",
        add_task: "작업 추가",
        details: "세부 사항",
        no_tasks: "아직 작업이 없습니다.",
        no_activity: "아직 활동이 없습니다.",
        move_element: "{{name}}을(를) {{coords}}로 이동",
        edit_area: "{{extra}} 영역 {{areaName}} 수정",
        delete_area: "영역 {{areaName}} 삭제",
        edit_note: "{{extra}} 노트 {{noteTitle}} 수정",
        delete_note: "노트 {{noteTitle}} 삭제",
        edit_table: "{{extra}} 테이블 {{tableName}} 수정",
        delete_table: "테이블 {{tableName}} 삭제",
        edit_type: "{{extra}} 유형 {{typeName}} 수정",
        delete_type: "유형 {{typeName}} 삭제",
        add_relationship: "관계 추가",
        edit_relationship: "{{extra}} 관계 {{refName}} 수정",
        delete_relationship: "관계 {{refName}} 삭제",
        not_found: "찾을 수 없음",
        pick_db: "데이터베이스 선택",
        generic: "일반",
        generic_description:
            "일반 다이어그램은 모든 SQL 유형으로 내보낼 수 있지만 지원하는 데이터 유형이 적습니다.",
        enums: "열거형",
        add_enum: "열거형 추가",
        edit_enum: "{{extra}} 열거형 {{enumName}} 수정",
        delete_enum: "열거형 삭제",
        enum_w_no_name: "이름 없는 열거형이 발견되었습니다",
        enum_w_no_values: "'{{enumName}}' 열거형에 값이 없습니다",
        duplicate_enums: "'{{enumName}}'라는 이름의 열거형이 중복되었습니다",
        no_enums: "열거형 없음",
        no_enums_text: "여기에 열거형을 정의하세요",
        declare_array: "배열 선언",
        empty_index_name:
            "'{{tableName}}' 테이블에 이름 없는 인덱스가 선언되었습니다",
        didnt_find_diagram: "이런! 다이어그램을 찾을 수 없습니다.",
        unsigned: "부호 없음",
    },
};

export {ko, korean};
