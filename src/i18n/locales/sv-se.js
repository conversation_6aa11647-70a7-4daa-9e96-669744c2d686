const swedish = {
    name: "Swedish",
    native_name: "<PERSON><PERSON>",
    code: "sv",
};

const sv = {
    translation: {
        report_bug: "Rapportera ett fel",
        import_from: "Importera",
        import: "<PERSON>mporter<PERSON>",
        file: "Fil",
        new: "<PERSON>y",
        new_window: "<PERSON><PERSON><PERSON> fönster",
        open: "<PERSON><PERSON><PERSON>",
        save: "Spara",
        save_as: "Spara som",
        save_as_template: "Spara som mall",
        template_saved: "Mall sparad!",
        rename: "Ändra namn",
        delete_diagram: "Radera diagram",
        are_you_sure_delete_diagram:
            "Är du säker på att du vill radera diagrammet? Denna åtgärd går inte att ångra.",
        oops_smth_went_wrong: "Oj! Något gick fel.",
        import_diagram: "Importera diagram",
        import_from_source: "Importera från SQL",
        export_as: "Exportera som",
        export_source: "Exportera SQL",
        models: "<PERSON><PERSON>",
        exit: "<PERSON>v<PERSON><PERSON><PERSON>",
        edit: "<PERSON><PERSON><PERSON>",
        undo: "<PERSON><PERSON><PERSON>",
        redo: "<PERSON><PERSON><PERSON> om",
        clear: "<PERSON><PERSON>",
        are_you_sure_clear:
            "<PERSON>r du säker på att du vill ta bort dina ändringar? Denna åtgärd går inte att ångra.",
        cut: "Klipp ut",
        copy: "Kopiera",
        paste: "Klistra",
        duplicate: "Duplicera",
        delete: "Radera",
        copy_as_image: "Kopiera som bild",
        view: "Visa",
        header: "Menylinje",
        sidebar: "Sidopanel",
        issues: "Fel",
        presentation_mode: "Presentationsläge",
        strict_mode: "Strikt läge",
        field_details: "Fältdetaljer",
        reset_view: "Återställ vy",
        show_grid: "Visa rutnät",
        show_cardinality: "Visa kardinalitet",
        theme: "Tema",
        light: "Ljust",
        dark: "Mörkt",
        zoom_in: "Zooma in",
        zoom_out: "Zooma ut",
        fullscreen: "Helskärm",
        settings: "Inställningar",
        show_timeline: "Visa tidslinje",
        autosave: "Autospara",
        panning: "Panorering",
        show_debug_coordinates: "Visa felsökningskoordinater",
        transform: "Transformera",
        viewbox: "Visningsruta",
        cursor_coordinates: "Markörkoordinater",
        coordinate_space: "Koordinatsystem",
        coordinate_space_screen: "Skärm",
        coordinate_space_diagram: "Diagram",
        table_width: "Tabell-bredd",
        language: "Språk",
        flush_storage: "Töm lagring",
        are_you_sure_flush_storage:
            "Är du säker på att du vill tömma lagringen? Detta kommer radera alla dina diagram och anpassade mallar och går inte att ångra.",
        storage_flushed: "Lagring tömd",
        help: "Hjälp",
        shortcuts: "Kortkommandon",
        ask_on_discord: "Fråga oss på Discord",
        feedback: "Feedback",
        no_changes: "Inga ändringar",
        loading: "Laddar...",
        last_saved: "Senast sparad",
        saving: "Sparar...",
        failed_to_save: "Kunde inte spara",
        fit_window_reset: "Anpassa fönster / Återställ",
        zoom: "Zoom",
        add_table: "Lägg till tabell",
        add_area: "Lägg till område",
        add_note: "Lägg till anteckning",
        add_type: "Lägg till typ",
        to_do: "Att göra",
        tables: "Tabeller",
        relationships: "Relationer",
        subject_areas: "Ämnesområden",
        notes: "Anteckningar",
        types: "Typer",
        search: "Sök...",
        no_tables: "Inga tabeller",
        no_tables_text: "Börja bygga ditt diagram!",
        no_relationships: "Inga relationer",
        no_relationships_text: "Dra för att koppla fält och skapa relationer!",
        no_subject_areas: "Inga ämnesområden",
        no_subject_areas_text: "Lägg till ämnesområden för att gruppera tabeller!",
        no_notes: "Inga anteckningar",
        no_notes_text: "Använd anteckningar för att lägga till extra information",
        no_types: "Inga typer",
        no_types_text: "Skapa dina egna anpassade datatyper",
        no_issues: "Inga fel upptäcktes.",
        strict_mode_is_on_no_issues:
            "Strikt läge är påslaget, så inga fel kommer att visas.",
        name: "Namn",
        type: "Typ",
        null: "Null",
        not_null: "Inte null",
        primary: "Primär",
        unique: "Unik",
        autoincrement: "Autoinkrement",
        default_value: "Standardvärde",
        check: "Kontrollera uttryck",
        this_will_appear_as_is:
            "*Detta kommer att visas i det genererade skriptet som det är.",
        comment: "Kommentar",
        add_field: "Lägg till fält",
        values: "Värden",
        size: "Storlek",
        precision: "Precision",
        set_precision: "Ställ in precision: 'storlek, siffror'",
        use_for_batch_input: "Använd för batch-inmatning",
        indices: "Index",
        add_index: "Lägg till index",
        select_fields: "Välj fält",
        title: "Titel",
        not_set: "Inte satt",
        foreign: "Främmande",
        cardinality: "Kardinalitet",
        on_update: "Vid uppdatering",
        on_delete: "Vid radering",
        swap: "Byt",
        one_to_one: "En-till-en",
        one_to_many: "En-till-många",
        many_to_one: "Många-till-en",
        content: "Innehåll",
        types_info:
            "Denna funktion är avsedd för objekt-relationella DBMS som PostgreSQL.\nOm avsett för MySQL eller MariaDB, kommer en JSON-typ att genereras med motsvarande JSON-valideringskontroll.\nOm avsett för SQLite, kommer den att översättas till en BLOB.\nOm avsett för MSSQL, kommer ett typ-alias skapas upp till det första fältet.",
        table_deleted: "Tabell raderad",
        area_deleted: "Område raderat",
        note_deleted: "Anteckning raderad",
        relationship_deleted: "Relation raderad",
        type_deleted: "Typ raderad",
        cannot_connect: "Kan inte ansluta, kolumnerna har olika typer",
        copied_to_clipboard: "Kopierad till urklipp",
        create_new_diagram: "Skapa nytt diagram",
        cancel: "Avbryt",
        open_diagram: "Öppna diagram",
        rename_diagram: "Byt namn på diagram",
        export: "Exportera",
        export_image: "Exportera bild",
        create: "Skapa",
        confirm: "Bekräfta",
        last_modified: "Senast ändrad",
        drag_and_drop_files:
            "Dra och släpp filen här eller klicka för att ladda upp.",
        upload_sql_to_generate_diagrams:
            "Ladda upp en SQL-fil för att auto-generera dina tabeller och kolumner.",
        overwrite_existing_diagram: "Skriv över befintligt diagram",
        only_mysql_supported:
            "*För närvarande stöds endast uppladdning av MySQL-skript.",
        blank: "Tom",
        filename: "Filnamn",
        table_w_no_name: "Deklarerade en tabell utan namn",
        duplicate_table_by_name: "Duplicerad tabell med namnet '{{tableName}}'",
        empty_field_name: "Tomt fält `namn` i tabell '{{tableName}}'",
        empty_field_type: "Tomt fält `typ` i tabell '{{tableName}}'",
        no_values_for_field:
            "'{{fieldName}}'-fältet i tabellen '{{tableName}}' är av typen `{{type}}`, men inga värden är specificerade",
        default_doesnt_match_type:
            "Standardvärdet för fältet '{{fieldName}}' i tabellen '{{tableName}}' matchar inte dess typ",
        not_null_is_null:
            "'{{fieldName}}'-fältet i tabellen '{{tableName}}' är NOT NULL, men har standardvärdet NULL",
        duplicate_fields:
            "Duplicerade fält med namnet '{{fieldName}}' i tabellen '{{tableName}}'",
        duplicate_index:
            "Duplicerat index med namnet '{{indexName}}' i tabellen '{{tableName}}'",
        empty_index: "Index i tabellen '{{tableName}}' indexerar inga kolumner",
        no_primary_key: "Tabellen '{{tableName}}' har ingen primärnyckel",
        type_with_no_name: "Deklarerade en typ utan namn",
        duplicate_types: "Duplicerade typer med namnet '{{typeName}}'",
        type_w_no_fields: "Deklarerade en tom typ '{{typeName}}' utan fält",
        empty_type_field_name: "Tomt fält `namn` i typen '{{typeName}}'",
        empty_type_field_type: "Tomt fält `typ` i typen '{{typeName}}'",
        no_values_for_type_field:
            "'{{fieldName}}'-fältet av typen '{{typeName}}' är av typen `{{type}}`, men inga värden är specificerade",
        duplicate_type_fields:
            "Duplicerade fält med namnet '{{fieldName}}' av typen '{{typeName}}'",
        duplicate_reference: "Duplicerad referens med namnet '{{refName}}'",
        circular_dependency: "Cirkulärt beroende gällande tabellen '{{refName}}'",
        timeline: "Tidslinje",
        priority: "Prioritet",
        none: "Ingen",
        low: "Låg",
        medium: "Medel",
        high: "Hög",
        sort_by: "Sortera efter",
        my_order: "Min ordning",
        completed: "Slutförd",
        alphabetically: "Alfabetiskt",
        add_task: "Lägg till uppgift",
        details: "Detaljer",
        no_tasks: "Du har inga uppgifter ännu.",
        no_activity: "Du har ingen aktivitet ännu.",
        move_element: "Flytta {{name}} till {{coords}}",
        edit_area: "{{extra}} Redigera område {{areaName}}",
        delete_area: "Radera område {{areaName}}",
        edit_note: "{{extra}} Redigera anteckning {{noteTitle}}",
        delete_note: "Radera anteckning {{noteTitle}}",
        edit_table: "{{extra}} Redigera tabell {{tableName}}",
        delete_table: "Radera tabell {{tableName}}",
        edit_type: "{{extra}} Redigera typ {{typeName}}",
        delete_type: "Radera typ {{typeName}}",
        add_relationship: "Lägg till relation",
        edit_relationship: "{{extra}} Redigera relation {{refName}}",
        delete_relationship: "Radera relation {{refName}}",
        not_found: "Inte hittad",
        pick_db: "Välj en databas",
        generic: "Generisk",
        generic_description:
            "Generiska diagram kan exporteras till samtliga SQL-varianter, men stöder få datatyper.",
        enums: "Enum",
        add_enum: "Lägg till enum",
        edit_enum: "{{extra}} Redigera enum {{enumName}}",
        delete_enum: "Radera enum",
        enum_w_no_name: "Hittade enum utan namn",
        enum_w_no_values: "Hittade enum '{{enumName}}' utan värden",
        duplicate_enums: "Duplicerade enum med namnet '{{enumName}}'",
        no_enums: "Inga enum",
        no_enums_text: "Definiera enum här",
        declare_array: "Deklarera array",
        empty_index_name:
            "Deklarerade ett index utan namn i tabell '{{tableName}}'",
        didnt_find_diagram: "Oj! Kunde inte hitta diagrammet.",
        unsigned: "Osignerad",
        share: "Dela",
        copy_link: "Kopiera länk",
        readme: "README",
        failed_to_load: "Kunde inte ladda. Se till att länken är korrekt.",
        share_info:
            "* Att dela denna länk kommer inte att skapa en live samarbetssession.",
    },
};

export {sv, swedish};
