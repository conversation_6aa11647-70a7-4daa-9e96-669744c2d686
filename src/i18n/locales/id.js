const indonesian = {
    name: "Indonesian",
    native_name: "Bahasa Indonesia",
    code: "id",
};

const id = {
    translation: {
        report_bug: "Laporkan bug",
        import_from: "Impor",
        import: "Impor",
        file: "Berkas",
        new: "Baru",
        new_window: "Jendela baru",
        open: "Buka",
        save: "Simpan",
        save_as: "Simpan sebagai",
        save_as_template: "Simpan sebagai template",
        template_saved: "Template tersimpan!",
        rename: "Ubah nama",
        delete_diagram: "Hapus diagram",
        are_you_sure_delete_diagram:
            "Apakah Anda yakin ingin menghapus diagram ini? Operasi ini tidak bisa dibatalkan.",
        oops_smth_went_wrong: "Ups! Ada yang salah.",
        import_diagram: "Impor diagram",
        import_from_source: "Impor dari SQL",
        export_as: "Ekspor sebagai",
        export_source: "Ekspor SQL",
        models: "Model",
        exit: "Keluar",
        edit: "Edit",
        undo: "Batalkan",
        redo: "Ulangi",
        clear: "<PERSON><PERSON><PERSON><PERSON>",
        are_you_sure_clear:
            "Apakah Anda yakin ingin membersihkan diagram ini? Ini tidak bisa dibatalkan.",
        cut: "Potong",
        copy: "Salin",
        paste: "Tempel",
        duplicate: "Gandakan",
        delete: "Hapus",
        copy_as_image: "Salin sebagai gambar",
        view: "Tampilan",
        header: "Menu bar",
        sidebar: "Sidebar",
        issues: "Masalah",
        presentation_mode: "Mode presentasi",
        strict_mode: "Mode ketat",
        field_details: "Detail bidang",
        reset_view: "Atur ulang tampilan",
        show_grid: "Tampilkan grid",
        show_cardinality: "Tampilkan kardinalitas",
        theme: "Tema",
        light: "Terang",
        dark: "Gelap",
        zoom_in: "Perbesar",
        zoom_out: "Perkecil",
        fullscreen: "Layar penuh",
        settings: "Pengaturan",
        show_timeline: "Tampilkan garis waktu",
        autosave: "Simpan otomatis",
        panning: "Geser",
        show_debug_coordinates: "Tampilkan koordinat debug",
        transform: "Transformasi",
        viewbox: "Kotak tampilan",
        cursor_coordinates: "Koordinat kursor",
        coordinate_space: "Ruang",
        coordinate_space_screen: "Layar",
        coordinate_space_diagram: "Diagram",
        table_width: "Lebar tabel",
        language: "Bahasa",
        flush_storage: "Bersihkan penyimpanan",
        are_you_sure_flush_storage:
            "Apakah Anda yakin ingin membersihkan penyimpanan? Ini akan menghapus semua diagram dan template kustom Anda secara permanen.",
        storage_flushed: "Penyimpanan dibersihkan",
        help: "Bantuan",
        shortcuts: "Pintasan",
        ask_on_discord: "Tanya kami di Discord",
        feedback: "Umpan balik",
        no_changes: "Tidak ada perubahan",
        loading: "Memuat...",
        last_saved: "Terakhir disimpan",
        saving: "Menyimpan...",
        failed_to_save: "Gagal menyimpan",
        fit_window_reset: "Sesuaikan jendela / Atur ulang",
        zoom: "Zoom",
        add_table: "Tambah tabel",
        add_area: "Tambah area",
        add_note: "Tambah catatan",
        add_type: "Tambah tipe",
        to_do: "Yang harus dilakukan",
        tables: "Tabel",
        relationships: "Hubungan",
        subject_areas: "Area subjek",
        notes: "Catatan",
        types: "Jenis",
        search: "Cari...",
        no_tables: "Tidak ada tabel",
        no_tables_text: "Mulai bangun diagram Anda!",
        no_relationships: "Tidak ada hubungan",
        no_relationships_text:
            "Seret untuk menghubungkan bidang dan membentuk hubungan!",
        no_subject_areas: "Tidak ada area subjek",
        no_subject_areas_text: "Tambahkan area subjek untuk mengelompokkan tabel!",
        no_notes: "Tidak ada catatan",
        no_notes_text: "Gunakan catatan untuk mencatat informasi tambahan",
        no_types: "Tidak ada tipe",
        no_types_text: "Buat tipe data kustom Anda sendiri",
        no_issues: "Tidak ada masalah yang terdeteksi.",
        strict_mode_is_on_no_issues:
            "Mode ketat dimatikan sehingga tidak ada masalah yang akan ditampilkan.",
        name: "Nama",
        type: "Jenis",
        null: "Kosong",
        not_null: "Tidak kosong",
        primary: "Utama",
        unique: "Unik",
        autoincrement: "Autoincrement",
        default_value: "Nilai default",
        check: "Periksa ekspresi",
        this_will_appear_as_is:
            "*Ini akan muncul dalam skrip yang dihasilkan sebagaimana adanya.",
        comment: "Komentar",
        add_field: "Tambah bidang",
        values: "Nilai",
        size: "Ukuran",
        precision: "Presisi",
        set_precision: "Atur presisi: (ukuran, digit)",
        use_for_batch_input: "Gunakan , untuk input batch",
        indices: "Indeks",
        add_index: "Tambah indeks",
        select_fields: "Pilih bidang",
        title: "Judul",
        not_set: "Belum diatur",
        foreign: "Asing",
        cardinality: "Kardinalitas",
        on_update: "Pada pembaruan",
        on_delete: "Pada penghapusan",
        swap: "Tukar",
        one_to_one: "Satu ke satu",
        one_to_many: "Satu ke banyak",
        many_to_one: "Banyak ke satu",
        content: "Konten",
        types_info:
            "Fitur ini ditujukan untuk DBMS objek-relasional seperti PostgreSQL.\nJika digunakan untuk MySQL atau MariaDB, tipe JSON akan dihasilkan dengan validasi json yang sesuai.\nJika digunakan untuk SQLite, itu akan diterjemahkan menjadi BLOB.\nJika digunakan untuk MSSQL, alias tipe ke bidang pertama akan dihasilkan.",
        table_deleted: "Tabel dihapus",
        area_deleted: "Area dihapus",
        note_deleted: "Catatan dihapus",
        relationship_deleted: "Hubungan dihapus",
        type_deleted: "Jenis dihapus",
        cannot_connect:
            "Tidak dapat menghubungkan, kolom memiliki tipe yang berbeda",
        copied_to_clipboard: "Disalin ke papan klip",
        create_new_diagram: "Buat diagram baru",
        cancel: "Batal",
        open_diagram: "Buka diagram",
        rename_diagram: "Ubah nama diagram",
        export: "Ekspor",
        export_image: "Ekspor gambar",
        create: "Buat",
        confirm: "Konfirmasi",
        last_modified: "Terakhir diubah",
        drag_and_drop_files:
            "Seret dan lepas file di sini atau klik untuk mengunggah.",
        upload_sql_to_generate_diagrams:
            "Unggah file SQL untuk mengotomatiskan tabel dan kolom Anda.",
        overwrite_existing_diagram: "Timpa diagram yang ada",
        only_mysql_supported: "*Saat ini hanya memuat skrip MySQL yang didukung.",
        blank: "Kosong",
        filename: "Nama file",
        table_w_no_name: "Mendeklarasikan tabel tanpa nama",
        duplicate_table_by_name: "Tabel duplikat dengan nama '{{tableName}}'",
        empty_field_name: "Nama bidang `name` kosong dalam tabel '{{tableName}}'",
        empty_field_type: "Jenis bidang `type` kosong dalam tabel '{{tableName}}'",
        no_values_for_field:
            "Bidang '{{fieldName}}' dalam tabel '{{tableName}}' adalah tipe `{{type}}` tetapi tidak ada nilai yang ditentukan",
        default_doesnt_match_type:
            "Nilai default untuk bidang '{{fieldName}}' dalam tabel '{{tableName}}' tidak sesuai dengan tipenya",
        not_null_is_null:
            "Bidang '{{fieldName}}' dalam tabel '{{tableName}}' adalah TIDAK NULL tetapi memiliki nilai default NULL",
        duplicate_fields:
            "Bidang tabel duplikat dengan nama '{{fieldName}}' dalam tabel '{{tableName}}'",
        duplicate_index:
            "Indeks duplikat dengan nama '{{indexName}}' dalam tabel '{{tableName}}'",
        empty_index:
            "Indeks dalam tabel '{{tableName}}' tidak mengindeks kolom apapun",
        no_primary_key: "Tabel '{{tableName}}' tidak memiliki kunci utama",
        type_with_no_name: "Mendeklarasikan tipe tanpa nama",
        duplicate_types: "Tipe duplikat dengan nama '{{typeName}}'",
        type_w_no_fields: "Mendeklarasikan tipe '{{typeName}}' kosong tanpa bidang",
        empty_type_field_name:
            "Nama bidang `name` kosong dalam tipe '{{typeName}}'",
        empty_type_field_type:
            "Jenis bidang `type` kosong dalam tipe '{{typeName}}'",
        no_values_for_type_field:
            "Bidang '{{fieldName}}' dalam tipe '{{typeName}}' adalah tipe `{{type}}` tetapi tidak ada nilai yang ditentukan",
        duplicate_type_fields:
            "Bidang tipe duplikat dengan nama '{{fieldName}}' dalam tipe '{{typeName}}'",
        duplicate_reference: "Referensi duplikat dengan nama '{{refName}}'",
        circular_dependency:
            "Ketergantungan siklik yang melibatkan tabel '{{refName}}'",
        timeline: "Garis waktu",
        priority: "Prioritas",
        none: "Tidak ada",
        low: "Rendah",
        medium: "Sedang",
        high: "Tinggi",
        sort_by: "Urutkan berdasarkan",
        my_order: "Urutan saya",
        completed: "Selesai",
        alphabetically: "Alfabet",
        add_task: "Tambah tugas",
        details: "Detail",
        no_tasks: "Anda belum memiliki tugas.",
        no_activity: "Anda belum memiliki aktivitas.",
        move_element: "Pindahkan {{name}} ke {{coords}}",
        edit_area: "{{extra}} Edit area {{areaName}}",
        delete_area: "Hapus area {{areaName}}",
        edit_note: "{{extra}} Edit catatan {{noteTitle}}",
        delete_note: "Hapus catatan {{noteTitle}}",
        edit_table: "{{extra}} Edit tabel {{tableName}}",
        delete_table: "Hapus tabel {{tableName}}",
        edit_type: "{{extra}} Edit tipe {{typeName}}",
        delete_type: "Hapus tipe {{typeName}}",
        add_relationship: "Tambah hubungan",
        edit_relationship: "{{extra}} Edit hubungan {{refName}}",
        delete_relationship: "Hapus hubungan {{refName}}",
        not_found: "Tidak ditemukan",
        pick_db: "Pilih basis data",
        generic: "Generik",
        generic_description:
            "Diagram generik dapat diekspor ke SQL mana pun tetapi mendukung sedikit tipe data.",
        enums: "Enums",
        add_enum: "Tambah enum",
        edit_enum: "{{extra}} Edit enum {{enumName}}",
        delete_enum: "Hapus enum",
        enum_w_no_name: "Menemukan enum tanpa nama",
        enum_w_no_values: "Menemukan enum '{{enumName}}' tanpa nilai",
        duplicate_enums: "Enum duplikat dengan nama '{{enumName}}'",
        no_enums: "Tidak ada enum",
        no_enums_text: "Definisikan enum di sini",
        declare_array: "Deklarasikan array",
        empty_index_name:
            "Mendeklarasikan indeks tanpa nama dalam tabel '{{tableName}}'",
        didnt_find_diagram: "Ups! Tidak menemukan diagram.",
    },
};

export {id, indonesian};
