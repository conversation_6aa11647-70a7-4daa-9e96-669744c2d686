const italian = {
    name: "Italian",
    native_name: "Italiano",
    code: "it",
};

const it = {
    translation: {
        report_bug: "Segnalare un bug",
        import_from: "Importa",
        import: "Importa",
        file: "File",
        new: "Nuovo",
        new_window: "Nuova Finestra",
        open: "Apri",
        save: "Salva",
        save_as: "Salva come",
        save_as_template: "Salva come modello",
        template_saved: "Modello salvato!",
        rename: "Rino<PERSON>",
        delete_diagram: "Elimina diagramma",
        are_you_sure_delete_diagram:
            "Sei sicuro di voler eliminare il diagramma? L'operazione è irreversibile.",
        oops_smth_went_wrong: "Oops! Qualcosa è andato storto.",
        import_diagram: "Importa diagramma",
        import_from_source: "Importa da SQL",
        export_as: "Esporta come",
        export_source: "Esporta SQL",
        models: "Modelli",
        exit: "Esci",
        edit: "Modifica",
        undo: "<PERSON><PERSON><PERSON>",
        redo: "Riprist<PERSON>",
        clear: "<PERSON><PERSON><PERSON>",
        are_you_sure_clear:
            "Sei sicuro di voler resettare il diagramma? L'operazione è irreversibile.",
        cut: "Taglia",
        copy: "Copia",
        paste: "Incolla",
        duplicate: "Duplica",
        delete: "Elimina",
        copy_as_image: "Copia come immagine",
        view: "Visualizza",
        header: "Intestazione",
        sidebar: "Barra laterale",
        issues: "Problemi",
        presentation_mode: "Presentazione",
        strict_mode: "Modalità strict",
        field_details: "Dettagli sul campo",
        reset_view: "Ristabilire vista",
        show_grid: "Mostra griglia",
        snap_to_grid: "Aggancia alla griglia",
        show_datatype: "Mostra tipo di dato",
        show_cardinality: "Mostra cardinalità",
        theme: "Tema",
        light: "Chiaro",
        dark: "Scuro",
        zoom_in: "Ingrandire",
        zoom_out: "Rimpicciolire",
        fullscreen: "Schermo intero",
        settings: "Opzioni",
        show_timeline: "Cronologia",
        autosave: "Salvataggio automatico",
        panning: "Panoramica",
        show_debug_coordinates: "Mostra coordinate di debug",
        transform: "Trasforma",
        viewbox: "Visualizza Box",
        cursor_coordinates: "Coordinate del cursore",
        coordinate_space: "Spazio",
        coordinate_space_screen: "Schermo",
        coordinate_space_diagram: "Diagramma",
        table_width: "Larghezza della tabella",
        language: "Lingua",
        flush_storage: "Pulizia memoria",
        are_you_sure_flush_storage:
            "Sei sicuro di voler eseguire la pulizia? L'operazione è irreversibile.",
        storage_flushed: "Pulizia eseguita!",
        help: "Aiuto",
        shortcuts: "Scorciatoie",
        ask_on_discord: "Contattaci su Discord",
        feedback: "Feedback",
        no_changes: "Nessun cambio",
        loading: "Caricamento in corso…",
        last_saved: "Ultimo salvataggio",
        saving: "Salvataggio in corso… ",
        failed_to_save: "Errore durante il salvataggio",
        fit_window_reset: "Adatta la finestra / Reimposta",
        zoom: "Zoom",
        add_table: "Aggiungi tabella",
        add_area: "Aggiungi area",
        add_note: "Aggiungi nota",
        add_type: "Aggiungi tipo",
        to_do: "Da fare",
        tables: "Tabelle",
        relationships: "Relazioni",
        subject_areas: "Aree tematiche",
        notes: "Note",
        types: "Tipi",
        search: "Cerca...",
        no_tables: "Nessuna tabella",
        no_tables_text: "Crea il tuo diagramma!",
        no_relationships: "Nessuna relazione",
        no_relationships_text: "Aggiungi una relazione tra tabelle!",
        no_subject_areas: "Nessuna area tematica",
        no_subject_areas_text: "Aggiungi un'area tematica!",
        no_notes: "Nessuna nota",
        no_notes_text: "Scrivi una nota!",
        no_types: "Nessun tipo",
        no_types_text: "Aggiungi un tipo!",
        no_issues: "Nessun problema da segnalare",
        strict_mode_is_on_no_issues:
            "La modalità strict è disattivata, i problemi non verrano segnalati.",
        name: "Nome",
        type: "Tipo",
        null: "Nullo",
        not_null: "Non nullo",
        primary: "Primario",
        unique: "Unico",
        autoincrement: "Autoincremento",
        default_value: "Valore predefinito",
        check: "Verifica l'espressione",
        this_will_appear_as_is: "Questo apparirà nello script generato così com'è.",
        comment: "Commento",
        add_field: "Aggiungi campo",
        values: "Valori",
        size: "Dimensione",
        precision: "Precisione",
        set_precision: "Imposta precisione: 'dimensione, cifre'",
        use_for_batch_input: "Usa, per l'elaborazione in batch",
        indices: "Indici",
        add_index: "Aggiungi indice",
        select_fields: "Seleziona campo",
        title: "Titolo",
        not_set: "Non impostato",
        foreign: "Esterno",
        cardinality: "Cardinalità",
        on_update: "Su aggiornamento",
        on_delete: "Su eliminazione",
        swap: "Scambiare",
        one_to_one: "Uno a uno",
        one_to_many: "Uno a molti",
        many_to_one: "Molti a uno",
        content: "Contenuto",
        types_info:
            "Questa funzione è destinata ai DBMS oggetto-relazionali, come PostgreSQL.\nSe si usa per MySQL o MariaDB, verrà generato un tipo JSON con il corrispondente controllo di validazione json.\nSe si usa per SQLite, verrà tradotto a BLOB.\nSe si usa per MSSQL, verrà generato un alias di tipo nel primo campo.",
        table_deleted: "Tabella rimossa",
        area_deleted: "Area rimossa",
        note_deleted: "Nota rimossa",
        relationship_deleted: "Relazione rimossa",
        type_deleted: "Tipo Rimosso",
        cannot_connect: "Connessione non riuscita, le colonne hanno tipi diversi",
        copied_to_clipboard: "Copiato negli appunti",
        create_new_diagram: "Crea nuovo diagramma",
        cancel: "Annulla",
        open_diagram: "Apri diagramma",
        rename_diagram: "Rinomina diagramma",
        export: "Esporta",
        export_image: "Esporta immagine",
        create: "Crea",
        confirm: "Accetta",
        last_modified: "Ultima modifica",
        drag_and_drop_files:
            "Trascina e rilascia qui il file o clicca per caricarlo.",
        upload_sql_to_generate_diagrams:
            "Carica un file sql per autogenerare le tabelle e le colonne.",
        overwrite_existing_diagram: "Sovrascrivi il diagramma esistente",
        only_mysql_supported:
            "*Per il momento è supportato solo il caricamento di script MySQL.",
        blank: "In bianco",
        filename: "Nome del file",
        table_w_no_name: "Dichiarata una tabella senza nome",
        duplicate_table_by_name: "Tabella duplicata con il nome '{{tableName}}'",
        empty_field_name: "Campo `name` vuoto nella tabella '{{tableName}}'",
        empty_field_type: "Campo `type` vuoto nella tabella '{{tableName}}'",
        no_values_for_field:
            "Il campo '{{fieldName}}' della tabella '{{tableName}}' è di tipo `{{type}}` ma non è stato specificato alcun valore",
        default_doesnt_match_type:
            "Il valore predefinito del campo '{{fieldName}}' della tabella '{{tableName}}' non corrisponde al suo tipo",
        not_null_is_null:
            "Il campo '{{fieldName}}' della tabella '{{tableName}}' è NOT NULL ma ha NULL come valore predefinito",
        duplicate_fields:
            "Campi della tabella duplicati con il nome '{{fieldName}}' nella tabella '{{tableName}}'",
        duplicate_index:
            "Indice duplicato con il nome '{{indexName}}' nella tabella '{{tableName}}'",
        empty_index: "L'indice nella tabella '{{tableName}}' non indicizza nessuna colonna",
        no_primary_key: "La tabella '{{tableName}}' non ha una chiave primaria",
        type_with_no_name: "Dichiarato un tipo senza nome",
        duplicate_types: "Tipi duplicati con il nome '{{typeName}}'",
        type_w_no_fields: "Dichiarato un tipo vuoto '{{typeName}}' senza alcun campo",
        empty_type_field_name: "Campo `name` vuoto nel tipo '{{typeName}}'",
        empty_type_field_type: "Campo `type` vuoto nel tipo '{{typeName}}'",
        no_values_for_type_field:
            "Il campo '{{fieldName}}' del tipo '{{typeName}}' è di tipo `{{type}}` ma non è stato specificato alcun valore",
        duplicate_type_fields:
            "Campi di tipo duplicati con il nome '{{fieldName}}' nel tipo '{{typeName}}'",
        duplicate_reference: "Riferimento duplicato con il nome '{{refName}}'",
        circular_dependency: "Dipendenza circolare riguardante la tabella '{{refName}}'",
        timeline: "Cronologia",
        priority: "Priorità",
        none: "Nessuna",
        low: "Bassa",
        medium: "Media",
        high: "Alta",
        sort_by: "Ordinare per",
        my_order: "Il mio ordine",
        completed: "Completato",
        alphabetically: "Alfabeticamente",
        add_task: "Aggiungi compito",
        details: "Dettagli",
        no_tasks: "Non ci sono compiti da svolgere.",
        no_activity: "Non ci sono ancora attività.",
        move_element: "Muovi {{name}} a {{coords}}",
        edit_area: "{{extra}} Modifica area {{areaName}}",
        delete_area: "Elimina area {{areaName}}",
        edit_note: "{{extra}} Modifica nota {{noteTitle}}",
        delete_note: "Elimina nota {{noteTitle}}",
        edit_table: "{{extra}} Modifica tabella {{tableName}}",
        delete_table: "Elimina tabella {{tableName}}",
        edit_type: "{{extra}} Modifica tipo {{typeName}}",
        delete_type: "Elimina tipo {{typeName}}",
        add_relationship: "Aggiungi relazione",
        edit_relationship: "{{extra}} Modifica relazione {{refName}}",
        delete_relationship: "Elimina relazione {{refName}}",
        not_found: "Nessun risultato",
        pick_db: "Scegli il database",
        generic: "Generico",
        generic_description:
            "I diagrammi generici possono essere esportati in qualsiasi formato SQL, ma supportano un numero limitato di tipi di dati.",
        enums: "Enums",
        add_enum: "Aggiungi enum",
        edit_enum: "{{extra}} Modifica enum {{enumName}}",
        delete_enum: "Elimina enum",
        enum_w_no_name: "Trovato enum senza nome",
        enum_w_no_values: "Trovato enum '{{enumName}}' senza alcun valore",
        duplicate_enums: "Enum duplicato con il nome '{{enumName}}'",
        no_enums: "Nessun enum",
        no_enums_text: "Definisci qui gli enum",
        declare_array: "Dichiara array",
        empty_index_name: "Dichiarato un indice senza nome nella tabella '{{tableName}}'",
        didnt_find_diagram: "Ops! Impossibile trovare il diagramma.",
        unsigned: "Senza segno",
        share: "Condividi",
        unshare: "Annulla condivisione",
        copy_link: "Copia link",
        readme: "README",
        failed_to_load: "Caricamento non riuscito. Assicurati che il link sia corretto",
        share_info:
            "* La condivisione di questo link non consentirà di creare una sessione di collaborazione in tempo reale.",
        show_relationship_labels: "Mostra le etichette di relazione",
        docs: "Documentazione",
        supported_types: "Tipi di file supportati:",
        bulk_update: "Aggiornamento in blocco",
        multiselect: "Multiselettore",
        export_saved_data: "Esporta i dati salvati",
        dbml_view: "Vista DBML",
        tab_view: "Vista tabella",
    },
};

export {it, italian};
