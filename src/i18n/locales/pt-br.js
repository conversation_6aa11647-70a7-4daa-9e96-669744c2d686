const portuguese = {
    name: "Portuguese",
    native_name: "Português",
    code: "pt-BR",
};

const pt = {
    translation: {
        report_bug: "Reportar um erro",
        import_from: "Importar",
        import: "Importar",
        file: "Arquivo",
        new: "Novo",
        new_window: "Nova janela",
        open: "Abrir",
        save: "Salvar",
        save_as: "Salvar como",
        save_as_template: "Salvar como template",
        template_saved: "Template salvo!",
        rename: "Renomear",
        delete_diagram: "Excluir diagrama",
        are_you_sure_delete_diagram:
            "Tem certeza de que deseja excluir este diagrama? Esta ação é irreversível.",
        oops_smth_went_wrong: "Ops! Algo deu errado.",
        import_diagram: "Importar diagrama",
        import_from_source: "Importar de SQL",
        export_as: "Exportar como",
        export_source: "Exportar SQL",
        models: "Modelos",
        exit: "Sair",
        edit: "<PERSON>ar",
        undo: "<PERSON><PERSON><PERSON>",
        redo: "<PERSON><PERSON><PERSON>",
        clear: "<PERSON><PERSON>",
        are_you_sure_clear:
            "Tem certeza de que deseja limpar o diagrama? Isso é irreversível.",
        cut: "Recortar",
        copy: "Copiar",
        paste: "Colar",
        duplicate: "Duplicar",
        delete: "Excluir",
        copy_as_image: "Copiar como imagem",
        view: "Ver",
        header: "Barra de menu",
        sidebar: "Barra lateral",
        issues: "Problemas",
        presentation_mode: "Modo de apresentação",
        strict_mode: "Modo estrito",
        field_details: "Detalhes do campo",
        reset_view: "Redefinir visualização",
        show_grid: "Mostrar grade",
        show_datatype: "Mostrar tipo de dado",
        show_cardinality: "Mostrar cardinalidade",
        theme: "Tema",
        light: "Claro",
        dark: "Escuro",
        zoom_in: "Aumentar zoom",
        zoom_out: "Diminuir zoom",
        fullscreen: "Tela cheia",
        settings: "Configurações",
        show_timeline: "Mostrar linha do tempo",
        autosave: "Salvar automaticamente",
        panning: "Deslocar",
        show_debug_coordinates: "Mostrar coordenadas de depuração",
        transform: "Transformar",
        viewbox: "Área de visualização",
        cursor_coordinates: "Coordenadas do cursor",
        coordinate_space: "Espaço de coordenadas",
        coordinate_space_screen: "Tela",
        coordinate_space_diagram: "Diagrama",
        table_width: "Largura da tabela",
        language: "Idioma",
        flush_storage: "Limpar armazenamento",
        are_you_sure_flush_storage:
            "Tem certeza de que deseja limpar o armazenamento? Isso irá excluir permanentemente todos os seus diagramas e modelos personalizados.",
        storage_flushed: "Armazenamento limpo",
        help: "Ajuda",
        shortcuts: "Atalhos",
        ask_on_discord: "Pergunte no Discord",
        feedback: "Feedback",
        no_changes: "Sem alterações",
        loading: "Carregando...",
        last_saved: "Última vez salvo às",
        saving: "Salvando...",
        failed_to_save: "Falha ao salvar",
        fit_window_reset: "Ajustar janela / Redefinir",
        zoom: "Zoom",
        add_table: "Adicionar tabela",
        add_area: "Adicionar área",
        add_note: "Adicionar nota",
        add_type: "Adicionar tipo",
        to_do: "Para fazer",
        tables: "Tabelas",
        relationships: "Relacionamentos",
        subject_areas: "Áreas de assunto",
        notes: "Notas",
        types: "Tipos",
        search: "Buscar...",
        no_tables: "Sem tabelas",
        no_tables_text: "Comece a construir seu diagrama!",
        no_relationships: "Sem relacionamentos",
        no_relationships_text:
            "Arraste para conectar campos e formar relacionamentos!",
        no_subject_areas: "Sem áreas de assunto",
        no_subject_areas_text: "Adicione áreas de assunto para agrupar tabelas!",
        no_notes: "Sem notas",
        no_notes_text: "Use notas para registrar informações extras",
        no_types: "Sem tipos",
        no_types_text: "Crie seus próprios tipos de dados personalizados",
        no_issues: "Nenhum problema foi detectado.",
        strict_mode_is_on_no_issues:
            "O modo estrito está desativado, portanto, nenhum problema será exibido.",
        name: "Nome",
        type: "Tipo",
        null: "Nulo",
        not_null: "Não nulo",
        nullable: "Nulo",
        primary: "Primário",
        unique: "Único",
        autoincrement: "Auto incremento",
        default_value: "Padrão",
        check: "Verificar expressão",
        this_will_appear_as_is: "*Isso aparecerá no script gerado como está.",
        comment: "Comentário",
        add_field: "Adicionar campo",
        values: "valores",
        size: "Tamanho",
        precision: "Precisão",
        set_precision: "Definir precisão: (tamanho, dígitos)",
        use_for_batch_input: "Usar , para entrada em massa",
        indices: "Índices",
        add_index: "Adicionar índice",
        select_fields: "Selecionar campos",
        title: "Título",
        not_set: "Não definido",
        foreign: "Estrangeiro",
        cardinality: "Cardinalidade",
        on_update: "Na atualização",
        on_delete: "Na exclusão",
        swap: "Trocar",
        one_to_one: "Um para um",
        one_to_many: "Um para muitos",
        many_to_one: "Muitos para um",
        content: "Conteúdo",
        types_info:
            "Este recurso destina-se a SGBDs objeto-relacionais como PostgreSQL.\nSe usado para MySQL ou MariaDB, será gerado um tipo JSON com a validação json correspondente.\nSe usado para SQLite, será traduzido para um BLOB.\nSe usado para MSSQL, será gerado um alias de tipo para o primeiro campo.",
        table_deleted: "Tabela excluída",
        area_deleted: "Área excluída",
        note_deleted: "Nota excluída",
        relationship_deleted: "Relacionamento excluído",
        type_deleted: "Tipo excluído",
        cannot_connect: "Não é possível conectar, as colunas têm tipos diferentes",
        copied_to_clipboard: "Copiado para a área de transferência",
        create_new_diagram: "Criar novo diagrama",
        cancel: "Cancelar",
        open_diagram: "Abrir diagrama",
        rename_diagram: "Renomear diagrama",
        export: "Exportar",
        export_image: "Exportar imagem",
        create: "Criar",
        confirm: "Confirmar",
        last_modified: "Última modificação",
        drag_and_drop_files:
            "Arraste e solte o arquivo aqui ou clique para fazer upload.",
        upload_sql_to_generate_diagrams:
            "Faça o upload de um arquivo SQL para gerar automaticamente suas tabelas e colunas.",
        overwrite_existing_diagram: "Substituir diagrama existente",
        only_mysql_supported:
            "*Por enquanto, apenas o carregamento de scripts MySQL é suportado.",
        blank: "Em branco",
        filename: "Nome do arquivo",
        table_w_no_name: "Declarada uma tabela sem nome",
        duplicate_table_by_name: "Tabela duplicada com o nome '{{tableName}}'",
        empty_field_name: "Campo `name` vazio na tabela '{{tableName}}'",
        empty_field_type: "Campo `type` vazio na tabela '{{tableName}}'",
        no_values_for_field:
            "O campo '{{fieldName}}' da tabela '{{tableName}}' é do tipo `{{type}}`, mas nenhum valor foi especificado",
        default_doesnt_match_type:
            "O valor padrão para o campo '{{fieldName}}' na tabela '{{tableName}}' não corresponde ao seu tipo",
        not_null_is_null:
            "O campo '{{fieldName}}' da tabela '{{tableName}}' é NOT NULL mas tem o valor padrão NULL",
        duplicate_fields:
            "Campos de tabela duplicados pelo nome '{{fieldName}}' na tabela '{{tableName}}'",
        duplicate_index:
            "Índice duplicado pelo nome '{{indexName}}' na tabela '{{tableName}}'",
        empty_index: "O índice na tabela '{{tableName}}' não indexa colunas",
        no_primary_key: "A tabela '{{tableName}}' não tem chave primária",
        type_with_no_name: "Declarado um tipo sem nome",
        duplicate_types: "Tipos duplicados pelo nome '{{typeName}}'",
        type_w_no_fields: "Declarado um tipo vazio '{{typeName}}' sem campos",
        empty_type_field_name: "Campo `name` vazio no tipo '{{typeName}}'",
        empty_type_field_type: "Campo `type` vazio no tipo '{{typeName}}'",
        no_values_for_type_field:
            "O campo '{{fieldName}}' do tipo '{{typeName}}' é do tipo `{{type}}` mas nenhum valor foi especificado",
        duplicate_type_fields:
            "Campos de tipo duplicados pelo nome '{{fieldName}}' no tipo '{{typeName}}'",
        duplicate_reference: "Referência duplicada pelo nome '{{refName}}'",
        circular_dependency: "Dependência circular envolvendo tabela '{{refName}}'",
        timeline: "Linha do tempo",
        priority: "Prioridade",
        none: "Nenhuma",
        low: "Baixa",
        medium: "Média",
        high: "Alta",
        sort_by: "Ordenar por",
        my_order: "Minha ordem",
        completed: "Concluído",
        alphabetically: "Alfabeticamente",
        add_task: "Adicionar tarefa",
        details: "Detalhes",
        no_tasks: "Você ainda não possui tarefas.",
        no_activity: "Você ainda não possui atividades.",
        move_element: "Mover {{name}} para {{coords}}",
        edit_area: "{{extra}} Editar área {{areaName}}",
        delete_area: "Excluir área {{areaName}}",
        edit_note: "{{extra}} Editar nota {{noteTitle}}",
        delete_note: "Excluir nota {{noteTitle}}",
        edit_table: "{{extra}} Editar tabela {{tableName}}",
        delete_table: "Excluir tabela {{tableName}}",
        edit_type: "{{extra}} Editar tipo {{typeName}}",
        delete_type: "Excluir tipo {{typeName}}",
        add_relationship: "Adicionar relacionamento",
        edit_relationship: "{{extra}} Editar relacionamento {{refName}}",
        delete_relationship: "Excluir relacionamento {{refName}}",
        not_found: "Não encontrado",
        pick_db: "Escolha um banco de dados",
        generic: "Genérico",
        generic_description:
            "Diagramas genéricos podem ser exportados para qualquer tipo de SQL, mas suportam poucos tipos de dados.",
        enums: "Enums",
        add_enum: "Adicionar enum",
        edit_enum: "{{extra}} Editar enum {{enumName}}",
        delete_enum: "Excluir enum",
        enum_w_no_name: "Enum encontrado sem nome",
        enum_w_no_values: "Enum '{{enumName}}' encontrado sem valores",
        duplicate_enums: "Enums duplicados com o nome '{{enumName}}'",
        no_enums: "Nenhum enum",
        no_enums_text: "Defina enums aqui",
        declare_array: "Declarar array",
        empty_index_name: "Índice sem nome declarado na tabela '{{tableName}}'",
        didnt_find_diagram: "Ops! Diagrama não encontrado.",
        unsigned: "Sem sinal (unsigned)",
        share: "Compartilhar",
        unshare: "Deixar de compartilhar",
        copy_link: "Copiar link",
        readme: "LEIAME",
        failed_to_load: "Falha ao carregar. Verifique se o link está correto.",
        share_info:
            "* Compartilhar este link não criará uma sessão de colaboração em tempo real.",
        show_relationship_labels: "Mostrar rótulos de relacionamento",
        docs: "Documentação",
        supported_types: "Tipos de arquivo suportados:",
        bulk_update: "Atualização em massa",
        multiselect: "Seleção múltipla",
        export_saved_data: "Exportar dados salvos",
    },
};

export {pt, portuguese};
