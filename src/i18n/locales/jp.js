const japanese = {
    name: "Japanese",
    native_name: "Japanese",
    code: "jp",
};

const jp = {
    translation: {
        report_bug: "バグを報告",
        import_from: "インポート",
        import: "インポート",
        file: "ファイル",
        new: "新規",
        new_window: "新しいウィンドウ",
        open: "開く",
        save: "保存",
        save_as: "名前を付けて保存",
        save_as_template: "テンプレートとして保存",
        template_saved: "テンプレートが保存されました！",
        rename: "名前を変更",
        delete_diagram: "ダイアグラムを削除",
        are_you_sure_delete_diagram:
            "このダイアグラムを削除してもよろしいですか？ この操作は取り消せません。",
        oops_smth_went_wrong: "おっと！何かがうまくいかなかった。",
        import_diagram: "ダイアグラムをインポート",
        import_from_source: "SQLからインポート",
        export_as: "としてエクスポート",
        export_source: "SQLをエクスポート",
        models: "モデル",
        exit: "終了",
        edit: "編集",
        undo: "元に戻す",
        redo: "やり直す",
        clear: "クリア",
        are_you_sure_clear:
            "ダイアグラムをクリアしてもよろしいですか？ この操作は取り消せません。",
        cut: "切り取り",
        copy: "コピー",
        paste: "貼り付け",
        duplicate: "複製",
        delete: "削除",
        copy_as_image: "画像としてコピー",
        view: "表示",
        header: "メニューバー",
        sidebar: "サイドバー",
        issues: "問題",
        presentation_mode: "プレゼンテーションモード",
        strict_mode: "厳格モード",
        field_details: "フィールドの詳細",
        reset_view: "ビューをリセット",
        show_grid: "グリッドを表示",
        show_cardinality: "カーディナリティを表示",
        theme: "テーマ",
        light: "ライト",
        dark: "ダーク",
        zoom_in: "ズームイン",
        zoom_out: "ズームアウト",
        fullscreen: "全画面表示",
        settings: "設定",
        show_timeline: "タイムラインを表示",
        autosave: "自動保存",
        panning: "パンニング",
        show_debug_coordinates: "デバッグ座標を表示",
        transform: "変換",
        viewbox: "ビュー・ボックス",
        cursor_coordinates: "カーソルの座標",
        coordinate_space: "空間",
        coordinate_space_screen: "スクリーン",
        coordinate_space_diagram: "ダイアグラム",
        table_width: "テーブル幅",
        language: "言語",
        flush_storage: "ストレージを消去",
        are_you_sure_flush_storage:
            "ストレージを消去してもよろしいですか？ これにより、すべてのダイアグラムとカスタムテンプレートが取り消し不能に削除されます。",
        storage_flushed: "ストレージが消去されました",
        help: "ヘルプ",
        shortcuts: "ショートカット",
        ask_on_discord: "Discordで質問する",
        feedback: "フィードバック",
        no_changes: "変更なし",
        loading: "読み込み中...",
        last_saved: "最後の保存",
        saving: "保存中...",
        failed_to_save: "保存に失敗しました",
        fit_window_reset: "ウィンドウに合わせる / リセット",
        zoom: "ズーム",
        add_table: "テーブルを追加",
        add_area: "エリアを追加",
        add_note: "ノートを追加",
        add_type: "タイプを追加",
        to_do: "やるべきこと",
        tables: "テーブル",
        relationships: "リレーションシップ",
        subject_areas: "主題領域",
        notes: "ノート",
        types: "タイプ",
        search: "検索...",
        no_tables: "テーブルがありません",
        no_tables_text: "ダイアグラムの作成を開始しましょう！",
        no_relationships: "リレーションシップがありません",
        no_relationships_text: "フィールドを接続してリレーションシップを作成！",
        no_subject_areas: "主題領域がありません",
        no_subject_areas_text: "主題領域を追加してテーブルをグループ化しましょう！",
        no_notes: "ノートがありません",
        no_notes_text: "追加情報の記録にノートを使用",
        no_types: "タイプがありません",
        no_types_text: "カスタムデータタイプを作成",
        no_issues: "問題は検出されていません。",
        strict_mode_is_on_no_issues:
            "厳格モードがオフになっているため、問題は表示されません。",
        name: "名前",
        type: "タイプ",
        null: "Null",
        not_null: "Not null",
        primary: "主キー",
        unique: "ユニーク",
        autoincrement: "オートインクリメント",
        default_value: "デフォルト",
        check: "チェック式",
        this_will_appear_as_is:
            "*これが生成されたスクリプトにそのまま表示されます。",
        comment: "コメント",
        add_field: "フィールドを追加",
        values: "値",
        size: "サイズ",
        precision: "精度",
        set_precision: "精度を設定: 'サイズ, 桁数'",
        use_for_batch_input: "バッチ入力に使用する場合は , を使用",
        indices: "インデックス",
        add_index: "インデックスを追加",
        select_fields: "フィールドを選択",
        title: "タイトル",
        not_set: "設定されていません",
        foreign: "外部キー",
        cardinality: "カーディナリティ",
        on_update: "更新時",
        on_delete: "削除時",
        swap: "入れ替え",
        one_to_one: "1対1",
        one_to_many: "1対多",
        many_to_one: "多対1",
        content: "内容",
        types_info:
            "この機能はPostgreSQLのようなオブジェクトリレーショナルDBMS向けです。\nMySQLやMariaDBで使用した場合、対応するjsonの検証チェックと共にJSONタイプが生成されます。\nSQLiteで使用した場合、BLOBに変換されます。\nMSSQLで使用した場合、最初のフィールドへのタイプエイリアスが生成されます。",
        table_deleted: "テーブルが削除されました",
        area_deleted: "エリアが削除されました",
        note_deleted: "ノートが削除されました",
        relationship_deleted: "リレーションシップが削除されました",
        type_deleted: "タイプが削除されました",
        cannot_connect: "接続できません、列のタイプが異なります",
        copied_to_clipboard: "クリップボードにコピーされました",
        create_new_diagram: "新しいダイアグラムを作成",
        cancel: "キャンセル",
        open_diagram: "ダイアグラムを開く",
        rename_diagram: "ダイアグラムの名前を変更",
        export: "エクスポート",
        export_image: "画像をエクスポート",
        create: "作成",
        confirm: "確認",
        last_modified: "最終更新",
        drag_and_drop_files:
            "ファイルをここにドラッグ＆ドロップするかクリックしてアップロードします。",
        upload_sql_to_generate_diagrams:
            "SQLファイルをアップロードしてテーブルと列を自動生成します。",
        overwrite_existing_diagram: "既存のダイアグラムを上書きする",
        only_mysql_supported:
            "*現在のところMySQLスクリプトの読み込みのみサポートされています。",
        blank: "空白",
        filename: "ファイル名",
        table_w_no_name: "名前のないテーブルが宣言されました",
        duplicate_table_by_name: "名前 '{{tableName}}' のテーブルが重複しています",
        empty_field_name: "テーブル '{{tableName}}' のフィールド `name` が空です",
        empty_field_type: "テーブル '{{tableName}}' のフィールド `type` が空です",
        no_values_for_field:
            "テーブル '{{tableName}}' のフィールド '{{fieldName}}' はタイプ `{{type}}` ですが、値が指定されていません",
        default_doesnt_match_type:
            "テーブル '{{tableName}}' のフィールド '{{fieldName}}' のデフォルト値がタイプと一致しません",
        not_null_is_null:
            "テーブル '{{tableName}}' のフィールド '{{fieldName}}' はNOT NULLですが、デフォルトはNULLです",
        duplicate_fields:
            "テーブル '{{tableName}}' 内に名前 '{{fieldName}}' のフィールドが重複しています",
        duplicate_index:
            "テーブル '{{tableName}}' 内に名前 '{{indexName}}' のインデックスが重複しています",
        empty_index:
            "テーブル '{{tableName}}' のインデックスは列をインデックスしていません",
        no_primary_key: "テーブル '{{tableName}}' に主キーがありません",
        type_with_no_name: "名前のないタイプが宣言されました",
        duplicate_types: "名前 '{{typeName}}' のタイプが重複しています",
        type_w_no_fields:
            "フィールドのない空のタイプ '{{typeName}}' が宣言されました",
        empty_type_field_name: "タイプ '{{typeName}}' のフィールド `name` が空です",
        empty_type_field_type: "タイプ '{{typeName}}' のフィールド `type` が空です",
        no_values_for_type_field:
            "タイプ '{{typeName}}' のフィールド '{{fieldName}}' はタイプ `{{type}}` ですが、値が指定されていません",
        duplicate_type_fields:
            "タイプ '{{typeName}}' 内に名前 '{{fieldName}}' のフィールドが重複しています",
        duplicate_reference: "名前 '{{refName}}' の参照が重複しています",
        circular_dependency:
            "テーブル '{{refName}}' を含む循環依存が検出されました",
        timeline: "タイムライン",
        priority: "優先度",
        none: "なし",
        low: "低",
        medium: "中",
        high: "高",
        sort_by: "並べ替え",
        my_order: "マイオーダー",
        completed: "完了",
        alphabetically: "アルファベット順",
        add_task: "タスクを追加",
        details: "詳細",
        no_tasks: "タスクはまだありません。",
        no_activity: "アクティビティはまだありません。",
        move_element: "{{name}} を {{coords}} に移動",
        edit_area: "{{extra}} エリア {{areaName}} を編集",
        delete_area: "エリア {{areaName}} を削除",
        edit_note: "{{extra}} ノート {{noteTitle}} を編集",
        delete_note: "ノート {{noteTitle}} を削除",
        edit_table: "{{extra}} テーブル {{tableName}} を編集",
        delete_table: "テーブル {{tableName}} を削除",
        edit_type: "{{extra}} タイプ {{typeName}} を編集",
        delete_type: "タイプ {{typeName}} を削除",
        add_relationship: "リレーションシップを追加",
        edit_relationship: "{{extra}} リレーションシップ {{refName}} を編集",
        delete_relationship: "リレーションシップ {{refName}} を削除",
        not_found: "見つかりません",
        pick_db: "データベースを選択",
        generic: "一般",
        generic_description:
            "一般的なダイアグラムは任意のSQL方言にエクスポートできますが、データタイプのサポートは限定的です。",
        enums: "列挙型",
        add_enum: "列挙型を追加",
        edit_enum: "{{extra}} 列挙型 {{enumName}} を編集",
        delete_enum: "列挙型を削除",
        enum_w_no_name: "名前のない列挙型が見つかりました",
        enum_w_no_values: "列挙型 '{{enumName}}' に値がありません",
        duplicate_enums: "名前 '{{enumName}}' の列挙型が重複しています",
        no_enums: "列挙型がありません",
        no_enums_text: "ここで列挙型を定義",
        declare_array: "配列を宣言",
        empty_index_name:
            "テーブル '{{tableName}}' 内で名前のないインデックスが宣言されました",
        didnt_find_diagram: "おっと！ダイアグラムが見つかりませんでした。",
        unsigned: "符号なし",
        share: "共有",
        copy_link: "リンクをコピー",
        readme: "README",
        failed_to_load:
            "読み込みに失敗しました。リンクが正しいか確認してください。",
        share_info:
            "* このリンクを共有してもリアルタイムコラボレーションセッションは作成されません。",
    },
};

export {jp, japanese};
