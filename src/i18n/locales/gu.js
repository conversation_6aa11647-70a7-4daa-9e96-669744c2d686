const gu<PERSON><PERSON> = {
    name: "Gujarati",
    native_name: "ગુજરાતી",
    code: "gu",
};

const gu = {
    translation: {
        report_bug: "બગ રિપોર્ટ કરો",
        import_from: "આયાત",
        import: "આયાત",
        file: "ફાઇલ",
        new: "નવું",
        new_window: "નવું વિંડો",
        open: "ખોલો",
        save: "સેવ",
        save_as: "સેવ તરીકે",
        save_as_template: "ટેમ્પલેટ તરીકે સેવ કરો",
        template_saved: "ટેમ્પલેટ સેવ થઇ ગયું!",
        rename: "નામ બદલો",
        delete_diagram: "ડાયાગ્રામ કાઢી નાંખો",
        are_you_sure_delete_diagram:
            "શું તમે ખરેખર આ ડાયાગ્રામ કાઢી નાંખવા માંગો છો? આ ક્રિયા અપરિવર્તનીય છે.",
        oops_smth_went_wrong: "અરે! કઈક ખોટું થઇ ગયું.",
        import_diagram: "ડાયાગ્રામ આયાત કરો",
        import_from_source: "SQL થી આયાત કરો",
        export_as: "રૂપે નિકાસ કરો",
        export_source: "SQL નિકાસ કરો",
        models: "મોડેલ્સ",
        exit: "બહાર નીકળો",
        edit: "સંપાદિત કરો",
        undo: "અનડૂ",
        redo: "રીડૂ",
        clear: "સાફ કરો",
        are_you_sure_clear:
            "શું તમે ખરેખર આ ડાયાગ્રામ સાફ કરવા માંગો છો? આ અપરિવર્તનીય છે.",
        cut: "કાપો",
        copy: "કૉપિ કરો",
        paste: "પેસ્ટ કરો",
        duplicate: "નકલ કરો",
        delete: "કાઢી નાંખો",
        copy_as_image: "ચિત્ર તરીકે નકલ કરો",
        view: "દ્રશ્ય",
        header: "મેનુબાર",
        sidebar: "સાઇડબાર",
        issues: "સમસ્યાઓ",
        presentation_mode: "પ્રસ્તુતિ સ્થિતિ",
        strict_mode: "સખત સ્થિતિ",
        field_details: "ફિલ્ડ વિગતો",
        reset_view: "દ્રશ્ય ફરીથી સેટ કરો",
        show_grid: "ગ્રિડ બતાવો",
        show_cardinality: "કાર્ડિનાલિટી બતાવો",
        theme: "થીમ",
        light: "પ્રકાશ",
        dark: "અંધકાર",
        zoom_in: "ઝૂમ ઇન",
        zoom_out: "ઝૂમ આઉટ",
        fullscreen: "પૂર્ણસ્ક્રીન",
        settings: "સેટિંગ્સ",
        show_timeline: "ટાઇમલાઇન બતાવો",
        autosave: "આટોસેવ",
        panning: "પેનિંગ",
        table_width: "ટેબલની પહોળાઈ",
        language: "ભાષા",
        flush_storage: "સ્ટોરેજ સાફ કરો",
        are_you_sure_flush_storage:
            "શું તમે ખરેખર સ્ટોરેજ સાફ કરવા માંગો છો? આ તમામ ડાયાગ્રામ અને કસ્ટમ ટેમ્પલેટ્સને હટાવી નાખશે.",
        storage_flushed: "સ્ટોરેજ સાફ કરાયું",
        help: "મદદ",
        shortcuts: "શૉર્ટકટ્સ",
        ask_on_discord: "અમને Discord પર પૂછો",
        feedback: "પ્રતિસાદ",
        no_changes: "કોઈ ફેરફાર નથી",
        loading: "લોડ થઇ રહ્યું છે...",
        last_saved: "છેલ્લે સેવ કરેલું",
        saving: "સેવ થઇ રહ્યું છે...",
        failed_to_save: "સેવ કરવામાં નિષ્ફળ",
        fit_window_reset: "વિંડો ફિટ કરો / ફરીથી સેટ કરો",
        zoom: "ઝૂમ",
        add_table: "ટેબલ ઉમેરો",
        add_area: "વિસ્તાર ઉમેરો",
        add_note: "નોંધ ઉમેરો",
        add_type: "પ્રકાર ઉમેરો",
        to_do: "કરવું",
        tables: "ટેબલ્સ",
        relationships: "સંબંધો",
        subject_areas: "વિષય ક્ષેત્રો",
        notes: "નોંધો",
        types: "પ્રકાર",
        search: "શોધો...",
        no_tables: "કોઈ ટેબલ્સ નથી",
        no_tables_text: "તમારું ડાયાગ્રામ બનાવવાનું શરૂ કરો!",
        no_relationships: "કોઈ સંબંધો નથી",
        no_relationships_text: "ફિલ્ડ્સને કનેક્ટ કરવા માટે ખેંચો અને સંબંધો બનાવો!",
        no_subject_areas: "કોઈ વિષય ક્ષેત્રો નથી",
        no_subject_areas_text: "વિષય ક્ષેત્રોમાં ટેબલ્સને ગ્રૂપ કરો!",
        no_notes: "કોઈ નોંધો નથી",
        no_notes_text: "વધારાની માહિતી રેકોર્ડ કરવા માટે નોંધોનો ઉપયોગ કરો",
        no_types: "કોઈ પ્રકાર નથી",
        no_types_text: "તમારા પોતાના કસ્ટમ ડેટા પ્રકારો બનાવો",
        no_issues: "કોઈ સમસ્યાઓ મળી નથી.",
        strict_mode_is_on_no_issues:
            "સખત સ્થિતિ બંધ છે એટલે કોઈ સમસ્યાઓ બતાવવામાં આવશે નહીં.",
        name: "નામ",
        type: "પ્રકાર",
        null: "Null",
        not_null: "નોટ null",
        primary: "મુખ્ય",
        unique: "અનન્ય",
        autoincrement: "સ્વયં વધારો",
        default_value: "મૂળ્ય",
        check: "ચેક અભિવ્યક્તિ",
        this_will_appear_as_is: "*આ જનરેટ થયેલ સ્ક્રિપ્ટમાં જેમ છે તેમ દેખાશે.",
        comment: "ટિપ્પણી",
        add_field: "ફિલ્ડ ઉમેરો",
        values: "મૂલ્યો",
        size: "કદ",
        precision: "પ્રમાણ",
        set_precision: "પ્રમાણ સેટ કરો: (કદ, ડિજિટ્સ)",
        use_for_batch_input: "બેચ ઇનપુટ માટે Use ,",
        indices: "ઇન્ડાયસીસ",
        add_index: "ઇન્ડેક્સ ઉમેરો",
        select_fields: "ફિલ્ડ્સ પસંદ કરો",
        title: "શીર્ષક",
        not_set: "સેટ નથી",
        foreign: "વિદેશી",
        cardinality: "કાર્ડિનાલિટી",
        on_update: "અપડેટ પર",
        on_delete: "કાઢી નાંખવા પર",
        swap: "સ્વેપ",
        one_to_one: "એકથી એક",
        one_to_many: "એકથી ઘણાં",
        many_to_one: "ઘણાંથી એક",
        content: "વિષયવસ્તુ",
        types_info:
            "આ સુવિધા object-relational DBMS જેમ કે PostgreSQL માટે છે.\nજો MySQL અથવા MariaDB માટે ઉપયોગમાં લેવામાં આવે છે, તો સંબંધિત json માન્યતા ચકાસણી સાથે એક JSON પ્રકાર જનરેટ થશે.\nજો SQLite માટે ઉપયોગમાં લેવામાં આવે છે તો તેને BLOBમાં રૂપાંતરિત કરવામાં આવશે.\nજો MSSQL માટે ઉપયોગમાં લેવામાં આવે છે તો પહેલા ફિલ્ડ માટે એક પ્રકાર ઉપનામ જનરેટ થશે.",
        table_deleted: "ટેબલ કાઢી નાખી",
        area_deleted: "વિસ્તાર કાઢી નાખ્યો",
        note_deleted: "નોંધ કાઢી નાખી",
        relationship_deleted: "સંબંધ કાઢી નાખ્યો",
        type_deleted: "પ્રકાર કાઢી નાખ્યો",
        cannot_connect: "કનેક્ટ કરી શકતા નથી, કોલમના પ્રકારો અલગ-અલગ છે",
        copied_to_clipboard: "ક્લિપબોર્ડ પર કૉપિ કર્યું",
        create_new_diagram: "નવું ડાયાગ્રામ બનાવો",
        cancel: "રદ કરો",
        open_diagram: "ડાયાગ્રામ ખોલો",
        rename_diagram: "ડાયાગ્રામનું નામ બદલો",
        export: "નિકાસ",
        export_image: "ચિત્ર નિકાસ કરો",
        create: "બનાવો",
        confirm: "પુષ્ટિ કરો",
        last_modified: "છેલ્લે સુધારેલા",
        drag_and_drop_files:
            "ફાઇલને અહીં ખેંચો અને છોડો અથવા અપલોડ કરવા માટે ક્લિક કરો.",
        upload_sql_to_generate_diagrams:
            "તમારી ટેબલ અને કોલમને આપમેળે જનરેટ કરવા માટે SQL ફાઇલ અપલોડ કરો.",
        overwrite_existing_diagram: "મોજુદા ડાયાગ્રામને ઓવરરાઈટ કરો",
        only_mysql_supported:
            "*હાલમાં ફક્ત MySQL સ્ક્રિપ્ટ્સ લોડ કરવા સપોર્ટેડ છે.",
        blank: "ખાલી",
        filename: "ફાઇલનામ",
        table_w_no_name: "નામ વિના ટેબલ ઘોષિત",
        duplicate_table_by_name: "નામ દ્વારા ડુપ્લિકેટ ટેબલ '{{tableName}}'",
        empty_field_name: "ટેબલ '{{tableName}}' માં ખાલી ફિલ્ડ `નામ`",
        empty_field_type: "ટેબલ '{{tableName}}' માં ખાલી ફીલ્ડ `પ્રકાર`",
        no_values_for_field:
            "ટેબલ '{{tableName}}' ના ફીલ્ડ '{{fieldName}}' નો પ્રકાર `{{type}}` છે પરંતુ કોઈ મૂલ્ય નિર્દિષ્ટ નથી",
        default_doesnt_match_type:
            "ટેબલ '{{tableName}}' માં ફીલ્ડ '{{fieldName}}' નું ડિફોલ્ટ મૂલ્ય તેના પ્રકાર સાથે મેળ ખાતું નથી",
        not_null_is_null:
            "ટેબલ '{{tableName}}' ના ફીલ્ડ '{{fieldName}}' નું મૂલ્ય NOT NULL છે પરંતુ ડિફોલ્ટ NULL છે",
        duplicate_fields:
            "ટેબલ '{{tableName}}' માં નામ '{{fieldName}}' વાળા ડુપ્લિકેટ ટેબલ ફીલ્ડ્સ",
        duplicate_index:
            "ટેબલ '{{tableName}}' માં નામ '{{indexName}}' વાળો ડુપ્લિકેટ ઈન્ડેક્સ",
        empty_index:
            "ટેબલ '{{tableName}}' માં ઈન્ડેક્સ કોઈ કૉલમ ઈન્ડેક્સ કરતું નથી",
        no_primary_key: "ટેબલ '{{tableName}}' માં કોઈ પ્રાથમિક કી નથી",
        type_with_no_name: "કોઈ નામ ન હોય તેવા પ્રકારને ઘોષિત કર્યું",
        duplicate_types: "નામ '{{typeName}}' વાળા ડુપ્લિકેટ પ્રકાર",
        type_w_no_fields:
            "કોઈ ફીલ્ડ્સ ન હોય તેવા પ્રકાર '{{typeName}}' ને ઘોષિત કર્યું",
        empty_type_field_name: "પ્રકાર '{{typeName}}' માં ખાલી ફીલ્ડ `નામ`",
        empty_type_field_type: "પ્રકાર '{{typeName}}' માં ખાલી ફીલ્ડ `પ્રકાર`",
        no_values_for_type_field:
            "પ્રકાર '{{typeName}}' ના ફીલ્ડ '{{fieldName}}' નો પ્રકાર `{{type}}` છે પરંતુ કોઈ મૂલ્ય નિર્દિષ્ટ નથી",
        duplicate_type_fields:
            "પ્રકાર '{{typeName}}' માં નામ '{{fieldName}}' વાળા ડુપ્લિકેટ પ્રકાર ફીલ્ડ્સ",
        duplicate_reference: "નામ '{{refName}}' વાળો ડુપ્લિકેટ સંદર્ભ",
        circular_dependency: "ટેબલ '{{refName}}' માં પરિપત્ર નિર્ભરતા",
        timeline: "સમયરેખા",
        priority: "પ્રાથમિકતા",
        none: "કોઈ નથી",
        low: "નીચું",
        medium: "મધ્યમ",
        high: "ઉચ્ચ",
        sort_by: "દ્વારા ક્રમબદ્ધ કરો",
        my_order: "મારો ક્રમ",
        completed: "પૂર્ણ",
        alphabetically: "વર્ણમાલાક્રમમાં",
        add_task: "કાર્ય ઉમેરો",
        details: "વિગતો",
        no_tasks: "તમારી પાસે હજુ સુધી કોઈ કાર્ય નથી.",
        no_activity: "તમારી પાસે હજુ સુધી કોઈ પ્રવૃત્તિ નથી.",
        move_element: "{{name}} ને {{coords}} પર ખસેડો",
        edit_area: "{{extra}} એડિટ ક્ષેત્ર {{areaName}}",
        delete_area: "વિસ્તાર કાઢી નાખો {{areaName}}",
        edit_note: "{{extra}} નોંધ સંપાદિત કરો {{noteTitle}}",
        delete_note: "નોંધ કાઢી નાખો {{noteTitle}}",
        edit_table: "{{extra}} ટેબલ સંપાદિત કરો {{tableName}}",
        delete_table: "ટેબલ કાઢી નાખો {{tableName}}",
        edit_type: "{{extra}} પ્રકાર સંપાદિત કરો {{typeName}}",
        delete_type: "પ્રકાર કાઢી નાખો {{typeName}}",
        add_relationship: "સંબંધ ઉમેરો",
        edit_relationship: "{{extra}} સંબંધ સંપાદિત કરો {{refName}}",
        delete_relationship: "સબંધ કાઢી નાખો {{refName}}",
        not_found: "મળ્યું નથી",
    },
};

export {gu, gujarati};
