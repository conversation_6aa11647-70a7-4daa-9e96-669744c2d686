const chinese = {
    name: "Simplified Chinese",
    native_name: "简体中文",
    code: "zh",
};

const zh = {
    translation: {
        report_bug: "报告问题",
        import_from: "导入",
        import: "导入",
        file: "文件",
        new: "新建",
        new_window: "在新标签中打开",
        open: "打开",
        save: "保存",
        save_as: "另存为",
        save_as_template: "保存为模板",
        template_saved: "模板已保存！",
        rename: "重命名",
        delete_diagram: "删除图表",
        are_you_sure_delete_diagram: "确定要删除此图表吗？此操作不可逆转。",
        oops_smth_went_wrong: "糟糕！出了些问题。",
        import_diagram: "导入图表",
        import_from_source: "导入 SQL 源代码",
        export_as: "导出为",
        export_source: "导出为 SQL 源代码",
        models: "模型",
        exit: "退出",
        edit: "编辑",
        undo: "撤销",
        redo: "恢复",
        clear: "清除",
        are_you_sure_clear: "确定要清除图表吗？此操作不可逆转。",
        cut: "剪切",
        copy: "复制",
        paste: "粘贴",
        duplicate: "克隆",
        delete: "删除",
        copy_as_image: "复制画布为图片",
        view: "视图",
        header: "菜单栏",
        sidebar: "侧边栏",
        issues: "问题",
        presentation_mode: "演示模式",
        strict_mode: "严格模式",
        field_details: "字段详情",
        reset_view: "重置视图",
        show_datatype: "显示数据类型",
        show_grid: "显示网格",
        show_cardinality: "显示关系",
        theme: "主题",
        light: "浅色",
        dark: "深色",
        zoom_in: "放大",
        zoom_out: "缩小",
        fullscreen: "全屏",
        settings: "设置",
        show_timeline: "修改记录",
        autosave: "自动保存",
        panning: "画布可拖动",
        show_relationship_labels: "显示关系标签",
        show_debug_coordinates: "显示调试坐标",
        transform: "变换",
        viewbox: "视图框",
        cursor_coordinates: "光标坐标",
        coordinate_space: "坐标空间",
        coordinate_space_screen: "屏幕",
        coordinate_space_diagram: "图表",
        table_width: "表格宽度",
        language: "语言",
        export_saved_data: "导出保存的数据",
        flush_storage: "清除存储",
        are_you_sure_flush_storage:
            "您确定要清除存储吗？此操作将无法恢复地删除您所有的图表和自定义模板。",
        storage_flushed: "存储已清空",
        help: "帮助",
        docs: "文档",
        shortcuts: "快捷键",
        ask_on_discord: "在 Discord 联系我们",
        feedback: "反馈",
        no_changes: "没有更改",
        loading: "加载中...",
        last_saved: "上次保存",
        saving: "保存中...",
        failed_to_save: "保存失败",
        fit_window_reset: "适应窗口/重置",
        zoom: "缩放",
        add_table: "添加表",
        add_area: "添加区域",
        add_note: "添加注释",
        add_type: "添加类型",
        to_do: "待办事项",
        tables: "表",
        relationships: "关系",
        subject_areas: "主题区域",
        notes: "注释",
        types: "类型",
        search: "搜索...",
        no_tables: "空空如也",
        no_tables_text: "开始构建您的图表！",
        no_relationships: "空空如也",
        no_relationships_text: "拖动以连接字段并形成关系！",
        no_subject_areas: "空空如也",
        no_subject_areas_text: "添加主题区域以分组表！",
        no_notes: "空空如也",
        no_notes_text: "使用注释记录额外信息",
        no_types: "空空如也",
        no_types_text: "制作您自己的自定义数据类型",
        no_issues: "未检测到问题。",
        strict_mode_is_on_no_issues: "严格模式已关闭，因此不会显示任何问题。",
        name: "名称",
        type: "类型",
        null: "空",
        not_null: "非空",
        primary: "主键",
        unique: "唯一",
        autoincrement: "自增",
        default_value: "默认值",
        check: "检查表达式",
        this_will_appear_as_is: "*此内容将按原样显示在生成的脚本中。",
        comment: "注释",
        add_field: "添加字段",
        values: "值",
        size: "大小",
        precision: "精度",
        set_precision: "设置精度：(大小，位数)",
        use_for_batch_input: "用于批量输入，使用逗号",
        indices: "索引",
        add_index: "添加索引",
        select_fields: "选择字段",
        title: "标题",
        not_set: "未设置",
        foreign: "外键",
        cardinality: "关系映射",
        on_update: "更新时",
        on_delete: "删除时",
        swap: "交换",
        one_to_one: "一对一",
        one_to_many: "一对多",
        many_to_one: "多对一",
        content: "内容",
        types_info:
            "此功能适用于像 PostgreSQL 这样的对象关系型数据库管理系统。\n如果用于 MySQL 或 MariaDB，将生成具有相应 JSON 验证检查的 JSON 类型。\n如果用于 SQLite，它将被转换为 BLOB。\n如果用于 MSSQL，将生成到第一个字段的类型别名。",
        table_deleted: "表已删除",
        area_deleted: "区域已删除",
        note_deleted: "注释已删除",
        relationship_deleted: "关系已删除",
        type_deleted: "类型已删除",
        cannot_connect: "无法连接，列具有不同的类型",
        copied_to_clipboard: "已复制到剪贴板",
        create_new_diagram: "创建新图表",
        cancel: "取消",
        open_diagram: "打开图表",
        rename_diagram: "重命名图表",
        export: "导出",
        export_image: "导出图像",
        create: "创建",
        confirm: "确认",
        last_modified: "最后修改",
        drag_and_drop_files: "拖放文件到此处或点击上传。",
        upload_sql_to_generate_diagrams: "上传 SQL 文件以自动生成表和列。",
        overwrite_existing_diagram: "覆盖现有图表",
        only_mysql_supported: "目前仅支持加载 MySQL 脚本。",
        blank: "空",
        filename: "文件名",
        table_w_no_name: "声明了一个没有名称的表",
        duplicate_table_by_name: "重复声明了名为 '{{tableName}}' 的表",
        empty_field_name: "表 '{{tableName}}' 中的字段 `name` 为空",
        empty_field_type: "表 '{{tableName}}' 中的字段 `type` 为空",
        no_values_for_field:
            "表 '{{tableName}}' 的 '{{fieldName}}' 字段类型为 `{{type}}`，但未指定任何值",
        default_doesnt_match_type:
            "表 '{{tableName}}' 中字段 '{{fieldName}}' 的默认值与其类型不匹配",
        not_null_is_null:
            "表 '{{tableName}}' 中的 '{{fieldName}}' 字段为 NOT NULL，但默认值为 NULL",
        duplicate_fields:
            "在表 '{{tableName}}' 中重复声明了名为 '{{fieldName}}' 的字段",
        duplicate_index:
            "在表 '{{tableName}}' 中重复声明了名为 '{{indexName}}' 的索引",
        empty_index: "在表 '{{tableName}}' 中的索引未指定任何列",
        no_primary_key: "表 '{{tableName}}' 没有主键",
        type_with_no_name: "声明了一个没有名称的类型",
        duplicate_types: "重复声明了名为 '{{typeName}}' 的类型",
        type_w_no_fields: "声明了一个没有字段的空类型 '{{typeName}}'",
        empty_type_field_name: "类型 '{{typeName}}' 中的字段 `name` 为空",
        empty_type_field_type: "类型 '{{typeName}}' 中的字段 `type` 为空",
        no_values_for_type_field:
            "类型 '{{typeName}}' 的 '{{fieldName}}' 字段类型为 `{{type}}`，但未指定任何值",
        duplicate_type_fields:
            "在自定义类 '{{typeName}}' 中重复声明了名为 '{{fieldName}}' 的字段",
        duplicate_reference: "重复声明了名为 '{{refName}}' 的引用",
        circular_dependency: "涉及到表 '{{refName}}' 的循环依赖",
        timeline: "时间轴",
        priority: "优先级",
        none: "无",
        low: "低",
        medium: "中",
        high: "高",
        sort_by: "排序方式",
        my_order: "我的排序",
        completed: "已完成",
        alphabetically: "按字母顺序",
        add_task: "添加任务",
        details: "详情",
        no_tasks: "您还没有任务。",
        no_activity: "您还没有活动。",
        move_element: "将 {{name}} 移动到 {{coords}}",
        edit_area: "{{extra}} 编辑区域 {{areaName}}",
        delete_area: "删除区域 {{areaName}}",
        edit_note: "{{extra}} 编辑注释 {{noteTitle}}",
        delete_note: "删除注释 {{noteTitle}}",
        edit_table: "{{extra}} 编辑表格 {{tableName}}",
        delete_table: "删除表格 {{tableName}}",
        edit_type: "{{extra}} 编辑类型 {{typeName}}",
        delete_type: "删除类型 {{typeName}}",
        add_relationship: "添加关系",
        edit_relationship: "{{extra}} 编辑关系 {{refName}}",
        delete_relationship: "删除关系 {{refName}}",
        not_found: "未找到",
        pick_db: "选择数据库",
        generic: "通用",
        generic_description:
            "通用图表可以导出为任何 SQL 格式，但仅支持有限的数据类型。",
        enums: "枚举",
        add_enum: "添加枚举",
        edit_enum: "{{extra}} 编辑枚举 {{enumName}}",
        delete_enum: "删除枚举",
        enum_w_no_name: "声明了一个没有名称的枚举",
        enum_w_no_values: "声明了一个没有值的枚举 '{{enumName}}'",
        duplicate_enums: "重复声明了名为 '{{enumName}}' 的枚举",
        no_enums: "没有枚举",
        no_enums_text: "在此定义您的枚举",
        declare_array: "声明数组",
        empty_index_name: "在表 '{{tableName}}' 中声明了一个没有名称的索引",
        didnt_find_diagram: "哎呀！找不到图表。",
        unsigned: "无符号",
        share: "分享",
        unshare: "取消分享",
        copy_link: "复制链接",
        readme: "说明文档",
        failed_to_load: "加载失败。请确认链接是否正确。",
        share_info:
            "* 分享此链接不会创建实时协作会话。",
        supported_types: "支持的文件类型：",
        bulk_update: "批量更新",
        multiselect: "多选",
        dbml_view: "DBML 视图",
        tab_view: "标签视图",
    },
};

export {zh, chinese};
