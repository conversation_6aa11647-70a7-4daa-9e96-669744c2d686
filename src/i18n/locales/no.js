const norwegian = {
    name: "Norwegian",
    native_name: "Nor<PERSON>",
    code: "no",
};

const no = {
    translation: {
        report_bug: "Rapporter en feil",
        import_from: "Importer",
        import: "Importer",
        file: "Fil",
        new: "Ny",
        new_window: "<PERSON><PERSON><PERSON> vindu",
        open: "<PERSON><PERSON><PERSON>",
        save: "Lagre",
        save_as: "Lagre som",
        save_as_template: "Lagre som mal",
        template_saved: "Malen er lagret!",
        rename: "Gi nytt navn",
        delete_diagram: "Slett diagram",
        are_you_sure_delete_diagram:
            "Er du sikker på at du vil slette dette diagrammet? Denne handlingen er ugjenkallelig.",
        oops_smth_went_wrong: "Oops! Noe gikk galt.",
        import_diagram: "Importer diagram",
        import_from_source: "Importer fra SQL",
        export_as: "Eksporter som",
        export_source: "Eksporter SQL",
        models: "Modeller",
        exit: "Av<PERSON><PERSON><PERSON>",
        edit: "<PERSON><PERSON>",
        undo: "<PERSON><PERSON>",
        redo: "<PERSON><PERSON><PERSON><PERSON> om",
        clear: "Tøm",
        are_you_sure_clear:
            "Er du sikker på at du vil tømme diagrammet? Dette er ugjenkallelig.",
        cut: "Klipp ut",
        copy: "Kopier",
        paste: "Lim inn",
        duplicate: "Dupliser",
        delete: "Slett",
        copy_as_image: "Kopier som bilde",
        view: "Vis",
        header: "Menylinje",
        sidebar: "Sidepanel",
        issues: "Problemer",
        presentation_mode: "Presentasjonsmodus",
        strict_mode: "Streng modus",
        field_details: "Feltdetaljer",
        reset_view: "Tilbakestill visning",
        show_grid: "Vis rutenett",
        show_cardinality: "Vis kardinalitet",
        theme: "Tema",
        light: "Lys",
        dark: "Mørk",
        zoom_in: "Zoom inn",
        zoom_out: "Zoom ut",
        fullscreen: "Fullskjerm",
        settings: "Innstillinger",
        show_timeline: "Vis tidslinje",
        autosave: "Automatisk lagring",
        panning: "Panorering",
        show_debug_coordinates: "Vis feilsøkingskoordinater",
        transform: "Transformer",
        viewbox: "Visningsboks",
        cursor_coordinates: "Markørkoordinater",
        coordinate_space: "Koordinatsystem",
        coordinate_space_screen: "Skjerm",
        coordinate_space_diagram: "Diagram",
        table_width: "Tabellbredde",
        language: "Språk",
        flush_storage: "Tøm lagring",
        are_you_sure_flush_storage:
            "Er du sikker på at du vil tømme lagringen? Dette vil ugjenkallelig slette alle dine diagrammer og egendefinerte maler.",
        storage_flushed: "Lagring tømt",
        help: "Hjelp",
        shortcuts: "Snarveier",
        ask_on_discord: "Spør oss på Discord",
        feedback: "Tilbakemelding",
        no_changes: "Ingen endringer",
        loading: "Laster...",
        last_saved: "Sist lagret",
        saving: "Lagrer...",
        failed_to_save: "Kunne ikke lagre",
        fit_window_reset: "Tilpass vindu / Tilbakestill",
        zoom: "Zoom",
        add_table: "Legg til tabell",
        add_area: "Legg til område",
        add_note: "Legg til notat",
        add_type: "Legg til type",
        to_do: "Oppgaver",
        tables: "Tabeller",
        relationships: "Relasjoner",
        subject_areas: "Emneområder",
        notes: "Notater",
        types: "Typer",
        search: "Søk...",
        no_tables: "Ingen tabeller",
        no_tables_text: "Begynn å bygge diagrammet ditt!",
        no_relationships: "Ingen relasjoner",
        no_relationships_text: "Dra for å koble felter og danne relasjoner!",
        no_subject_areas: "Ingen emneområder",
        no_subject_areas_text: "Legg til emneområder for å gruppere tabeller!",
        no_notes: "Ingen notater",
        no_notes_text: "Bruk notater for å legge til ekstra informasjon",
        no_types: "Ingen typer",
        no_types_text: "Lag dine egne egendefinerte datatyper",
        no_issues: "Ingen problemer ble oppdaget.",
        strict_mode_is_on_no_issues:
            "Streng modus er av, så ingen problemer vil bli vist.",
        name: "Navn",
        type: "Type",
        null: "Null",
        not_null: "Ikke null",
        primary: "Primær",
        unique: "Unik",
        autoincrement: "Autoinkrement",
        default_value: "Standardverdi",
        check: "Sjekk uttrykk",
        this_will_appear_as_is:
            "*Dette vil vises i det genererte skriptet som det er.",
        comment: "Kommentar",
        add_field: "Legg til felt",
        values: "Verdier",
        size: "Størrelse",
        precision: "Presisjon",
        set_precision: "Sett presisjon: 'størrelse, siffer'",
        use_for_batch_input: "Bruk , for batch-inndata",
        indices: "Indekser",
        add_index: "Legg til indeks",
        select_fields: "Velg felter",
        title: "Tittel",
        not_set: "Ikke satt",
        foreign: "Fremmed",
        cardinality: "Kardinalitet",
        on_update: "Ved oppdatering",
        on_delete: "Ved sletting",
        swap: "Bytt",
        one_to_one: "En-til-en",
        one_to_many: "En-til-mange",
        many_to_one: "Mange-til-en",
        content: "Innhold",
        types_info:
            "Denne funksjonen er ment for objekt-relasjonelle DBMS-er som PostgreSQL.\nHvis brukt for MySQL eller MariaDB, vil en JSON-type bli generert med tilsvarende JSON-valideringssjekk.\nHvis brukt for SQLite, vil det bli oversatt til en BLOB.\nHvis brukt for MSSQL, vil en typealias til det første feltet bli generert.",
        table_deleted: "Tabell slettet",
        area_deleted: "Område slettet",
        note_deleted: "Notat slettet",
        relationship_deleted: "Relasjon slettet",
        type_deleted: "Type slettet",
        cannot_connect: "Kan ikke koble, kolonnene har forskjellige typer",
        copied_to_clipboard: "Kopiert til utklippstavlen",
        create_new_diagram: "Opprett nytt diagram",
        cancel: "Avbryt",
        open_diagram: "Åpne diagram",
        rename_diagram: "Gi nytt navn til diagram",
        export: "Eksporter",
        export_image: "Eksporter bilde",
        create: "Opprett",
        confirm: "Bekreft",
        last_modified: "Sist endret",
        drag_and_drop_files: "Dra og slipp filen her eller klikk for å laste opp.",
        upload_sql_to_generate_diagrams:
            "Last opp en SQL-fil for å autogenerere dine tabeller og kolonner.",
        overwrite_existing_diagram: "Overskriv eksisterende diagram",
        only_mysql_supported: "*For tiden støttes kun lasting av MySQL-skript.",
        blank: "Blank",
        filename: "Filnavn",
        table_w_no_name: "Deklarerte en tabell uten navn",
        duplicate_table_by_name: "Duplikattabell med navnet '{{tableName}}'",
        empty_field_name: "Tomt felt `navn` i tabell '{{tableName}}'",
        empty_field_type: "Tomt felt `type` i tabell '{{tableName}}'",
        no_values_for_field:
            "'{{fieldName}}' feltet i tabellen '{{tableName}}' er av typen `{{type}}`, men ingen verdier er spesifisert",
        default_doesnt_match_type:
            "Standardverdien for feltet '{{fieldName}}' i tabellen '{{tableName}}' samsvarer ikke med dens type",
        not_null_is_null:
            "'{{fieldName}}' feltet i tabellen '{{tableName}}' er IKKE NULL, men har standardverdi NULL",
        duplicate_fields:
            "Duplikatfelter med navnet '{{fieldName}}' i tabell '{{tableName}}'",
        duplicate_index:
            "Duplikatindeks med navnet '{{indexName}}' i tabell '{{tableName}}'",
        empty_index: "Indeks i tabell '{{tableName}}' indekserer ingen kolonner",
        no_primary_key: "Tabellen '{{tableName}}' har ingen primærnøkkel",
        type_with_no_name: "Deklarerte en type uten navn",
        duplicate_types: "Duplikattyper med navnet '{{typeName}}'",
        type_w_no_fields: "Deklarerte en tom type '{{typeName}}' uten felter",
        empty_type_field_name: "Tomt felt `navn` i type '{{typeName}}'",
        empty_type_field_type: "Tomt felt `type` i type '{{typeName}}'",
        no_values_for_type_field:
            "'{{fieldName}}' feltet i typen '{{typeName}}' er av typen `{{type}}`, men ingen verdier er spesifisert",
        duplicate_type_fields:
            "Duplikatfelter med navnet '{{fieldName}}' i type '{{typeName}}'",
        duplicate_reference: "Duplikatreferanse med navnet '{{refName}}'",
        circular_dependency:
            "Sirkulær avhengighet som involverer tabell '{{refName}}'",
        timeline: "Tidslinje",
        priority: "Prioritet",
        none: "Ingen",
        low: "Lav",
        medium: "Middels",
        high: "Høy",
        sort_by: "Sorter etter",
        my_order: "Min rekkefølge",
        completed: "Fullført",
        alphabetically: "Alfabetisk",
        add_task: "Legg til oppgave",
        details: "Detaljer",
        no_tasks: "Du har ingen oppgaver ennå.",
        no_activity: "Du har ingen aktivitet ennå.",
        move_element: "Flytt {{name}} til {{coords}}",
        edit_area: "{{extra}} Rediger område {{areaName}}",
        delete_area: "Slett område {{areaName}}",
        edit_note: "{{extra}} Rediger notat {{noteTitle}}",
        delete_note: "Slett notat {{noteTitle}}",
        edit_table: "{{extra}} Rediger tabell {{tableName}}",
        delete_table: "Slett tabell {{tableName}}",
        edit_type: "{{extra}} Rediger type {{typeName}}",
        delete_type: "Slett type {{typeName}}",
        add_relationship: "Legg til relasjon",
        edit_relationship: "{{extra}} Rediger relasjon {{refName}}",
        delete_relationship: "Slett relasjon {{refName}}",
        not_found: "Ikke funnet",
        pick_db: "Velg en database",
        generic: "Generisk",
        generic_description:
            "Generiske diagrammer kan eksporteres til alle SQL-varianter, men støtter få datatyper.",
        enums: "Enum-er",
        add_enum: "Legg til enum",
        edit_enum: "{{extra}} Rediger enum {{enumName}}",
        delete_enum: "Slett enum",
        enum_w_no_name: "Fant enum uten navn",
        enum_w_no_values: "Fant enum '{{enumName}}' uten verdier",
        duplicate_enums: "Duplikat enum-er med navnet '{{enumName}}'",
        no_enums: "Ingen enum-er",
        no_enums_text: "Definer enum-er her",
        declare_array: "Deklarer array",
        empty_index_name: "Deklarerte en indeks uten navn i tabell '{{tableName}}'",
        didnt_find_diagram: "Oops! Fant ikke diagrammet.",
        unsigned: "Usignert",
        share: "Del",
        copy_link: "Kopier lenke",
        readme: "README",
        failed_to_load: "Kunne ikke laste. Forsikre deg om at lenken er korrekt.",
        share_info:
            "* Å dele denne lenken vil ikke opprette en live sanntidssamarbeidssession.",
    },
};

export {no, norwegian};
