const french = {
    name: "French",
    native_name: "Fran<PERSON>",
    code: "fr",
};

const fr = {
    translation: {
        report_bug: "Signaler un bug",
        import_from: "Importer de",
        import: "Importer",
        file: "Fichier",
        new: "Nouveau",
        new_window: "Nouvelle fenêtre",
        open: "Ouvrir",
        save: "Enregistrer",
        save_as: "Enregistrer sous",
        save_as_template: "Enregistrer en tant que modèle",
        template_saved: "Modèle enregistré!",
        rename: "Renommer",
        delete_diagram: "Supprimer le diagramme",
        are_you_sure_delete_diagram:
            "Êtes-vous sûr de vouloir supprimer ce diagramme? Cette action est irréversible.",
        oops_smth_went_wrong: "Oups! <PERSON><PERSON><PERSON> chose s'est mal passé.",
        import_diagram: "Importer un diagramme",
        import_from_source: "Importer depuis SQL",
        export_as: "Exporter en tant que",
        export_source: "Exporter SQL",
        models: "Mod<PERSON><PERSON>",
        exit: "Sortir",
        edit: "É<PERSON>er",
        undo: "Annuler",
        redo: "Ré<PERSON>bli<PERSON>",
        clear: "Effacer",
        are_you_sure_clear:
            "Êtes-vous sûr de vouloir effacer ce diagramme? Cette action est irréversible.",
        cut: "Couper",
        copy: "Copier",
        paste: "Coller",
        duplicate: "Dupliquer",
        delete: "Supprimer",
        copy_as_image: "Copier en tant qu'image",
        view: "Vue",
        header: "Barre de menu",
        sidebar: "Barre latérale",
        issues: "Problèmes",
        presentation_mode: "Mode présentation",
        strict_mode: "Mode strict",
        field_details: "Détails du champ",
        reset_view: "Réinitialiser la vue",
        show_grid: "Afficher la grille",
        show_cardinality: "Afficher la cardinalité",
        theme: "Thème",
        light: "Clair",
        dark: "Sombre",
        zoom_in: "Zoom avant",
        zoom_out: "Zoom arrière",
        fullscreen: "Plein écran",
        settings: "Paramètres",
        show_timeline: "Afficher la chronologie",
        autosave: "Sauvegarde automatique",
        panning: "Panoramique",
        table_width: "Largeur de la table",
        language: "Langue",
        flush_storage: "Vider le stockage",
        are_you_sure_flush_storage:
            "Êtes-vous sûr de vouloir vider le stockage? Cela supprimera de manière irréversible tous vos diagrammes et modèles personnalisés.",
        storage_flushed: "Stockage vidé",
        help: "Aide",
        shortcuts: "Raccourcis",
        ask_on_discord: "Demandez-nous sur Discord",
        feedback: "Retour d'information",
        no_changes: "Aucun changement",
        loading: "Chargement...",
        last_saved: "Dernière sauvegarde",
        saving: "Enregistrement...",
        failed_to_save: "Échec de l'enregistrement",
        fit_window_reset: "Ajuster / Réinitialiser la fenêtre",
        zoom: "Zoom",
        add_table: "Ajouter une table",
        add_area: "Ajouter une zone",
        add_note: "Ajouter une note",
        add_type: "Ajouter un type",
        to_do: "À faire",
        tables: "Tables",
        relationships: "Relations",
        subject_areas: "Sujets",
        notes: "Notes",
        types: "Types",
        search: "Rechercher...",
        no_tables: "Aucune table",
        no_tables_text: "Commencez à créer votre diagramme!",
        no_relationships: "Aucune relation",
        no_relationships_text:
            "Faites glisser pour connecter les champs et créer des relations!",
        no_subject_areas: "Aucune zone de sujet",
        no_subject_areas_text:
            "Regroupez les tables en ajoutant des zones de sujet!",
        no_notes: "Aucune note",
        no_notes_text:
            "Utilisez les notes pour enregistrer des informations supplémentaires",
        no_types: "Aucun type",
        no_types_text: "Créez vos propres types de données personnalisés",
        no_issues: "Aucun problème détecté.",
        strict_mode_is_on_no_issues:
            "Le mode strict est désactivé, donc aucun problème ne sera affiché.",
        name: "Nom",
        type: "Type",
        null: "Null",
        not_null: "Not Null",
        primary: "Primaire",
        unique: "Unique",
        autoincrement: "Auto-incrément",
        default_value: "Valeur par défaut",
        check: "Expression de vérification",
        this_will_appear_as_is: "*Ceci apparaîtra tel quel dans le script généré.",
        comment: "Commentaire",
        add_field: "Ajouter un champ",
        values: "Valeurs",
        size: "Taille",
        precision: "Précision",
        set_precision: "Définir la précision: (taille, chiffres)",
        use_for_batch_input: "Utiliser , pour les entrées par lot",
        indices: "Indices",
        add_index: "Ajouter un index",
        select_fields: "Sélectionner les champs",
        title: "Titre",
        not_set: "Non défini",
        foreign: "Étranger",
        cardinality: "Cardinalité",
        on_update: "Lors de la mise à jour",
        on_delete: "Lors de la suppression",
        swap: "Échanger",
        one_to_one: "Un à un",
        one_to_many: "Un à plusieurs",
        many_to_one: "Plusieurs à un",
        content: "Contenu",
        types_info:
            "Cette fonctionnalité est pour les SGBD orientés objets comme PostgreSQL.\nSi utilisée avec MySQL ou MariaDB, un type JSON sera généré avec une vérification de validité JSON correspondante.\nSi utilisée avec SQLite, elle sera convertie en BLOB.\nSi utilisée avec MSSQL, un alias de type sera généré pour le premier champ.",
        table_deleted: "Table supprimée",
        area_deleted: "Zone supprimée",
        note_deleted: "Note supprimée",
        relationship_deleted: "Relation supprimée",
        type_deleted: "Type supprimé",
        cannot_connect:
            "Impossible de connecter, les types de colonnes sont différents",
        copied_to_clipboard: "Copié dans le presse-papiers",
        create_new_diagram: "Créer un nouveau diagramme",
        cancel: "Annuler",
        open_diagram: "Ouvrir le diagramme",
        rename_diagram: "Renommer le diagramme",
        export: "Exporter",
        export_image: "Exporter l'image",
        create: "Créer",
        confirm: "Confirmer",
        last_modified: "Dernière modification",
        drag_and_drop_files:
            "Glissez et déposez les fichiers ici ou cliquez pour les télécharger.",
        upload_sql_to_generate_diagrams:
            "Téléchargez un fichier SQL pour générer automatiquement vos tables et colonnes.",
        overwrite_existing_diagram: "Écraser le diagramme existant",
        only_mysql_supported:
            "*Pour le moment, seuls les scripts MySQL peuvent être chargés.",
        blank: "Vide",
        filename: "Nom du fichier",
        table_w_no_name: "Table déclarée sans nom",
        duplicate_table_by_name: "Table en double par le nom '{{tableName}}'",
        empty_field_name: "Nom de champ vide dans la table '{{tableName}}'",
        empty_field_type: "Type de champ vide dans la table '{{tableName}}'",
        no_values_for_field:
            "Le champ '{{fieldName}}' de la table '{{tableName}}' a un type `{{type}}` mais aucune valeur n'est spécifiée",
        default_doesnt_match_type:
            "La valeur par défaut du champ '{{fieldName}}' de la table '{{tableName}}' ne correspond pas à son type",
        not_null_is_null:
            "Le champ '{{fieldName}}' de la table '{{tableName}}' est NOT NULL mais la valeur par défaut est NULL",
        duplicate_fields:
            "Champs de table en double nommés '{{fieldName}}' dans la table '{{tableName}}'",
        duplicate_index:
            "Index en double nommé '{{indexName}}' dans la table '{{tableName}}'",
        empty_index: "Index sans colonnes dans la table '{{tableName}}'",
        no_primary_key: "Aucune clé primaire dans la table '{{tableName}}'",
        type_with_no_name: "Type déclaré sans nom",
        duplicate_types: "Types en double nommés '{{typeName}}'",
        type_w_no_fields: "Type déclaré sans champs '{{typeName}}'",
        empty_type_field_name: "Nom de champ vide dans le type '{{typeName}}'",
        empty_type_field_type: "Type de champ vide dans le type '{{typeName}}'",
        no_values_for_type_field:
            "Le champ '{{fieldName}}' du type '{{typeName}}' a un type `{{type}}` mais aucune valeur n'est spécifiée",
        duplicate_type_fields:
            "Champs de type en double nommés '{{fieldName}}' dans le type '{{typeName}}'",
        duplicate_reference: "Référence en double nommée '{{refName}}'",
        circular_dependency: "Dépendance circulaire dans la table '{{refName}}'",
        timeline: "Chronologie",
        priority: "Priorité",
        none: "Aucun",
        low: "Faible",
        medium: "Moyen",
        high: "Élevé",
        sort_by: "Trier par",
        my_order: "Mon ordre",
        completed: "Terminé",
        alphabetically: "Alphabétiquement",
        add_task: "Ajouter une tâche",
        details: "Détails",
        no_tasks: "Vous n'avez pas encore de tâches.",
        no_activity: "Vous n'avez pas encore d'activité.",
        move_element: "Déplacer {{name}} à {{coords}}",
        edit_area: "{{extra}} Modifier la zone {{areaName}}",
        delete_area: "Supprimer la zone {{areaName}}",
        edit_note: "{{extra}} Modifier la note {{noteTitle}}",
        delete_note: "Supprimer la note {{noteTitle}}",
        edit_table: "{{extra}} Modifier la table {{tableName}}",
        delete_table: "Supprimer la table {{tableName}}",
        edit_type: "{{extra}} Modifier le type {{typeName}}",
        delete_type: "Supprimer le type {{typeName}}",
        add_relationship: "Ajouter une relation",
        edit_relationship: "{{extra}} Modifier la relation {{refName}}",
        delete_relationship: "Supprimer la relation {{refName}}",
        not_found: "Non trouvé",
        readme: "README",
    },
};

export {fr, french};
