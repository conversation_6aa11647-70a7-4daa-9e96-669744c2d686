const telugu = {
    name: "Telugu",
    native_name: "తెలుగు",
    code: "te",
};

const te = {
    translation: {
        report_bug: "బగ్ నివేదించండి",
        import_from: "దిగుమతి",
        import: "దిగుమతి",
        file: "ఫైల్",
        new: "క్రొత్త",
        new_window: "క్రొత్త విండో",
        open: "తెరవండి",
        save: "సేవ్",
        save_as: "క్రింద సేవ్",
        save_as_template: "టెంప్లేట్ క్రింద సేవ్",
        template_saved: "టెంప్లేట్ సేవ్ అయింది!",
        rename: "పేరు మార్చండి",
        delete_diagram: "డయాగ్రామ్ తొలగించండి",
        are_you_sure_delete_diagram:
            "మీరు ఈ డయాగ్రామ్ తొలగించాలని నిజంగా అనుకుంటున్నారా? ఇది తిరిగి పొందలేని చర్య.",
        oops_smth_went_wrong: "అయో! ఏదో తప్పు జరిగింది.",
        import_diagram: "డయాగ్రామ్ దిగుమతి",
        import_from_source: "SQL నుండి దిగుమతి",
        export_as: "క్రింద ఎగుమతి",
        export_source: "SQL ఎగుమతి",
        models: "మోడల్స్",
        exit: "నిష్క్రమణ",
        edit: "సవరించు",
        undo: "రద్దు చేయి",
        redo: "మళ్ళీ చేయి",
        clear: "స్పష్టంగా",
        are_you_sure_clear:
            "మీరు ఈ డయాగ్రామ్ ఖచ్చితంగా క్లియర్ చేయాలనుకుంటున్నారా? ఇది తిరిగి పొందలేని చర్య.",
        cut: "కట్ చేయండి",
        copy: "కాపీ చేయండి",
        paste: "పేస్ట్ చేయండి",
        duplicate: "డుప్లికేట్ చేయండి",
        delete: "తొలగించండి",
        copy_as_image: "చిత్రం కాపీ చేయి",
        view: "దృశ్యం",
        header: "మెనూబార్",
        sidebar: "సైడ్బార్",
        issues: "సమస్యలు",
        presentation_mode: "ప్రెజెంటేషన్ మోడ్",
        strict_mode: "స్ట్రిక్ట్ మోడ్",
        field_details: "ఫీల్డ్ వివరాలు",
        reset_view: "దృశ్యం రీసెట్ చేయి",
        show_grid: "గ్రిడ్ చూపించు",
        show_cardinality: "కార్డినాలిటీ చూపించు",
        theme: "థీమ్",
        light: "కాంతి",
        dark: "చీకటి",
        zoom_in: "జూమ్ ఇన్ చేయండి",
        zoom_out: "జూమ్ అవుట్ చేయండి",
        fullscreen: "పూర్తి తెర",
        settings: "సెట్టింగ్స్",
        show_timeline: "టైమ్‌లైన్ చూపించు",
        autosave: "ఆటోసేవ్",
        panning: "ప్యానింగ్",
        table_width: "పట్టిక వెడల్పు",
        language: "భాష",
        flush_storage: "స్టోరేజ్ క్లియర్ చేయి",
        are_you_sure_flush_storage:
            "మీరు స్టోరేజ్ ఖచ్చితంగా క్లియర్ చేయాలనుకుంటున్నారా? ఇది మీ అన్ని డయాగ్రామ్‌లు మరియు కస్టమ్ టెంప్లేట్లను తిరిగి పొందలేని విధంగా తొలగిస్తుంది.",
        storage_flushed: "స్టోరేజ్ క్లియర్ చేయబడింది",
        help: "సహాయం",
        shortcuts: "షార్ట్‌కట్లు",
        ask_on_discord: "Discord లో అడగండి",
        feedback: "ఫీడ్బాక్",
        no_changes: "ఏ మార్పులూ లేవు",
        loading: "లోడ్ అవుతోంది...",
        last_saved: "చివరిగా సేవ్ చేయబడింది",
        saving: "సేవ్ అవుతోంది...",
        failed_to_save: "సేవ్ చేయడంలో విఫలమైంది",
        fit_window_reset: "విండోకి సరిపోయే విధంగా / రీసెట్ చేయి",
        zoom: "జూమ్",
        add_table: "పట్టిక చేర్చండి",
        add_area: "ప్రాంతం చేర్చండి",
        add_note: "గమనిక చేర్చండి",
        add_type: "రకం చేర్చండి",
        to_do: "చేయవలసిన",
        tables: "పట్టికలు",
        relationships: "సంబంధాలు",
        subject_areas: "విషయ ప్రాంతాలు",
        notes: "గమనికలు",
        types: "రకాలు",
        search: "శోధించండి...",
        no_tables: "ఏ పట్టికలు లేవు",
        no_tables_text: "మీ డయాగ్రామ్ ను ప్రారంభించండి!",
        no_relationships: "ఏ సంబంధాలు లేవు",
        no_relationships_text:
            "ఫీల్డ్స్ కలుపుకోవడానికి డ్రాగ్ చేసి సంబంధం ఏర్పాటు చేయండి!",
        no_subject_areas: "ఏ విషయ ప్రాంతాలు లేవు",
        no_subject_areas_text: "పట్టికలను సమూహంగా సబ్జెక్ట్ ప్రాంతాలకు చేర్చండి!",
        no_notes: "ఏ గమనికలు లేవు",
        no_notes_text: "అదనపు సమాచారం రికార్డ్ చేసేందుకు గమనికలు ఉపయోగించండి",
        no_types: "ఏ రకాలు లేవు",
        no_types_text: "మీ సొంత కస్టమ్ డేటా రకాలను సృష్టించండి",
        no_issues: "ఏ సమస్యలు లభించలేదు.",
        strict_mode_is_on_no_issues:
            "స్ట్రిక్ట్ మోడ్ ఆఫ్ లో ఉంది కాబట్టి ఏ సమస్యలు చూపించబడవు.",
        name: "పేరు",
        type: "రకం",
        null: "Null",
        not_null: "Null కాదు",
        primary: "ప్రాధమిక",
        unique: "అద్వితీయ",
        autoincrement: "స్వీయ వృద్ధి",
        default_value: "మూల్యాన్ని అప్రమేయంగా చేయి",
        check: "తనిఖీ",
        this_will_appear_as_is:
            "*ఇది ఉత్పత్తి చేయబడిన స్క్రిప్ట్‌లో వంటి ప్రదర్శితం అవుతుంది.",
        comment: "వ్యాఖ్య",
        add_field: "ఫీల్డ్ చేర్చండి",
        values: "విలువలు",
        size: "పరిమాణం",
        precision: "సూక్ష్మత",
        set_precision: "సూక్ష్మత సెట్ చేయండి: (పరిమాణం, అంకెలు)",
        use_for_batch_input: "బ్యాచ్ ఇన్‌పుట్ కోసం ఉపయోగించండి",
        indices: "సూచికలు",
        add_index: "సూచిక చేర్చండి",
        select_fields: "ఫీల్డ్స్ ఎంచుకోండి",
        title: "శీర్షిక",
        not_set: "సెట్ చేయబడలేదు",
        foreign: "ఫారెన్",
        cardinality: "కార్డినాలిటీ",
        on_update: "నవీకరణపై",
        on_delete: "తొలగింపుపై",
        swap: "స్వాప్ చేయండి",
        one_to_one: "ఒకటి నుండి ఒకటి",
        one_to_many: "ఒకటి నుండి అనేక",
        many_to_one: "అనేక నుండి ఒకటి",
        content: "కంటెంట్",
        types_info:
            "ఈ లక్షణం object-relational DBMS లాంటి PostgreSQL కోసం ఉంది.\nMySQL లేదా MariaDB కోసం ఉపయోగించబడితే, సంబంధిత json చెల్లుబాటు తనిఖీతో ఒక JSON రకం ఉత్పత్తి అవుతుంది.\nSQLite కోసం ఉపయోగించబడితే, ఇది BLOB లోకి మార్చబడుతుంది.\nMSSQL కోసం ఉపయోగించబడితే, మొదటి ఫీల్డ్ కోసం ఒక రకం అలియాస్ ఉత్పత్తి అవుతుంది.",
        table_deleted: "పట్టిక తొలగించబడింది",
        area_deleted: "ప్రాంతం తొలగించబడింది",
        note_deleted: "గమనిక తొలగించబడింది",
        relationship_deleted: "సంబంధం తొలగించబడింది",
        type_deleted: "రకం తొలగించబడింది",
        cannot_connect: "కనెక్ట్ చేయలేరు, కాలమ్ రకాలు భిన్నంగా ఉన్నాయి",
        copied_to_clipboard: "క్లిప్‌బోర్డ్‌కు కాపీ చేయబడింది",
        create_new_diagram: "క్రొత్త డయాగ్రామ్ సృష్టించండి",
        cancel: "రద్దు చేయండి",
        open_diagram: "డయాగ్రామ్ తెరవండి",
        rename_diagram: "డయాగ్రామ్ పేరు మార్చండి",
        export: "ఎగుమతి",
        export_image: "చిత్రం ఎగుమతి చేయి",
        create: "సృష్టించండి",
        confirm: "నిర్ధారించండి",
        last_modified: "చివరిగా సవరించబడింది",
        drag_and_drop_files:
            "ఫైల్స్‌ను ఇక్కడికి లాగి వదిలివేయండి లేదా అప్‌లోడ్ చేయడానికి క్లిక్ చేయండి.",
        upload_sql_to_generate_diagrams:
            "మీ టేబుల్ మరియు కాలమ్స్‌ను స్వయంచాలకంగా ఉత్పత్తి చేయడానికి SQL ఫైల్‌ను అప్‌లోడ్ చేయండి.",
        overwrite_existing_diagram:
            "అన్నిప్రతుల మునుపటి ఆర్కెంట్ అంబి అవరు ఆర్కెదాన్ని చర్",
        only_mysql_supported:
            "*ప్రస్తుతం MySQL స్క్రిప్ట్స్ మాత్రమే లోడ్ చేయడానికి మద్దతు ఉంది.",
        blank: "ఖాళీ",
        filename: "ఫైల్ పేరు",
        table_w_no_name: "పేరు లేకుండా పట్టికని ప్రకటించారు",
        duplicate_table_by_name: "పేరుతో డుప్లికేట్ పట్టిక '{{tableName}}'",
        empty_field_name: "పట్టిక '{{tableName}}' లో ఖాళీ ఫీల్డ్ `పేరు`",
        empty_field_type: "పట్టిక '{{tableName}}' లో ఖాళీ ఫీల్డ్ `రకం`",
        no_values_for_field:
            "పట్టిక '{{tableName}}' లో ఫీల్డ్ '{{fieldName}}' రకం `{{type}}` కానీ విలువలు ఇవ్వబడలేదు",
        default_doesnt_match_type:
            "పట్టిక '{{table.name}}' లో ఫీల్డ్ '{{fieldName}}' యొక్క డిఫాల్ట్ విలువ రకంతో సరిపోలడం లేదు",
        not_null_is_null:
            "పట్టిక '{{tableName}}' లో ఫీల్డ్ '{{fieldName}}' యొక్క విలువ NOT NULL కానీ డిఫాల్ట్ NULL ఉంది",
        duplicate_fields:
            "పట్టిక '{{tableName}}' లో పేరుతో డుప్లికేట్ టేబుల్ ఫీల్డ్స్ '{{fieldName}}'",
        duplicate_index:
            "పట్టిక '{{tableName}}' లో పేరుతో డుప్లికేట్ ఇండెక్స్ '{{indexName}}'",
        empty_index: "పట్టిక '{{tableName}}' లో ఏ కాలమ్స్ ను ఇండెక్స్ చేయదు",
        no_primary_key: "పట్టిక '{{tableName}}' లో ప్రాధమిక కీ లేదు",
        type_with_no_name: "పేరు లేకుండా రకాన్ని ప్రకటించారు",
        duplicate_types: "పేరుతో డుప్లికేట్ రకాలు '{{typeName}}'",
        type_w_no_fields: "ఫీల్డ్స్ లేకుండా రకం '{{typeName}}' ను ప్రకటించారు",
        empty_type_field_name: "రకం '{{typeName}}' లో ఖాళీ ఫీల్డ్ `పేరు`",
        empty_type_field_type: "రకం '{{typeName}}' లో ఖాళీ ఫీల్డ్ `రకం`",
        no_values_for_type_field:
            "రకం '{{typeName}}' లో ఫీల్డ్ '{{fieldName}}' రకం `{{type}}` కానీ విలువలు ఇవ్వబడలేదు",
        duplicate_type_fields:
            "రకం '{{typeName}}' లో పేరుతో డుప్లికేట్ రకం ఫీల్డ్స్ '{{fieldName}}'",
        duplicate_reference: "పేరుతో డుప్లికేట్ రిఫరెన్స్ '{{refName}}'",
        circular_dependency: "పట్టిక '{{refName}}' లో సర్క్యులర్ డిపెండెన్సీ",
        timeline: "టైమ్‌లైన్",
        priority: "ప్రాధాన్యత",
        none: "ఏదీ లేదు",
        low: "తక్కువ",
        medium: "మధ్యస్థ",
        high: "అధిక",
        sort_by: "ద్వారా క్రమబద్ధీకరించండి",
        my_order: "నా క్రమం",
        completed: "పూర్తి",
        alphabetically: "అక్షర క్రమంలో",
        add_task: "పని చేర్చండి",
        details: "వివరాలు",
        no_tasks: "మీ వద్ద ఇప్పటివరకు ఏ పనులు లేవు.",
        no_activity: "మీ వద్ద ఇప్పటివరకు ఏ కార్యకలాపాలు లేవు.",
        move_element: "{{name}} ను {{coords}} వద్ద కదలించండి",
        edit_area: "{{extra}} ప్రాంతం సవరించండి {{areaName}}",
        delete_area: "ప్రాంతం తొలగించండి {{areaName}}",
        edit_note: "{{extra}} గమనిక సవరించండి {{noteTitle}}",
        delete_note: "గమనిక తొలగించండి {{noteTitle}}",
        edit_table: "{{extra}} పట్టిక సవరించండి {{tableName}}",
        delete_table: "పట్టిక తొలగించండి {{tableName}}",
        edit_type: "{{extra}} రకం సవరించండి {{typeName}}",
        delete_type: "రకం తొలగించండి {{typeName}}",
        add_relationship: "సంబంధం చేర్చండి",
        edit_relationship: "{{extra}} సంబంధం సవరించండి {{refName}}",
        delete_relationship: "సంబంధం తొలగించండి {{refName}}",
        not_found: "దొరకలేదు",
    },
};

export {te, telugu};
