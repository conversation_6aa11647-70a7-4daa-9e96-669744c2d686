const marathi = {
    name: "Marathi",
    native_name: "मराठी",
    code: "mr",
};

const mr = {
    translation: {
        report_bug: "बग रिपोर्ट करा",
        import_from: "आयात",
        import: "आयात",
        file: "फाइल",
        new: "नवीन",
        new_window: "नवीन विंडो",
        open: "उघडा",
        save: "जतन",
        save_as: "च्या रूपात जतन करा",
        save_as_template: "टेम्पलेट म्हणून जतन करा",
        template_saved: "टेम्पलेट जतन झाले!",
        rename: "नाव बदला",
        delete_diagram: "डायग्राम हटवा",
        are_you_sure_delete_diagram:
            "आपण खरोखर हा डायग्राम हटवू इच्छिता? ही क्रिया अपरिवर्तनीय आहे.",
        oops_smth_went_wrong: "अरे! काहीतरी चूक झाली.",
        import_diagram: "डायग्राम आयात करा",
        import_from_source: "SQL मधून आयात करा",
        export_as: "च्या रूपात निर्यात करा",
        export_source: "SQL निर्यात करा",
        models: "मॉडेल्स",
        exit: "बाहेर पडा",
        edit: "संपादन करा",
        undo: "पूर्ववत करा",
        redo: "पुन्हा करा",
        clear: "साफ करा",
        are_you_sure_clear:
            "आपण खरोखर हा डायग्राम साफ करू इच्छिता? ही क्रिया अपरिवर्तनीय आहे.",
        cut: "कापा",
        copy: "कॉपी करा",
        paste: "पेस्ट करा",
        duplicate: "प्रतिलिपी बनवा",
        delete: "हटवा",
        copy_as_image: "प्रतिमेच्या रूपात कॉपी करा",
        view: "दृश्य",
        header: "मेनूबार",
        sidebar: "साइडबार",
        issues: "समस्या",
        presentation_mode: "प्रस्तुतीकरण मोड",
        strict_mode: "कडक मोड",
        field_details: "फील्ड तपशील",
        reset_view: "दृश्य रीसेट करा",
        show_grid: "ग्रिड दाखवा",
        show_cardinality: "कार्डिनॅलिटी दाखवा",
        theme: "थीम",
        light: "प्रकाश",
        dark: "अंधार",
        zoom_in: "झूम इन",
        zoom_out: "झूम आऊट",
        fullscreen: "पूर्ण स्क्रीन",
        settings: "सेटिंग्स",
        show_timeline: "टाइमलाइन दाखवा",
        autosave: "ऑटोसेव",
        panning: "पॅनिंग",
        table_width: "टेबलची रुंदी",
        language: "भाषा",
        flush_storage: "स्टोरेज साफ करा",
        are_you_sure_flush_storage:
            "आपण खरोखर स्टोरेज साफ करू इच्छिता? हे सर्व आपले डायग्राम आणि कस्टम टेम्पलेट्स अपरिवर्तनीयपणे हटवेल.",
        storage_flushed: "स्टोरेज साफ केले",
        help: "मदत",
        shortcuts: "शॉर्टकट्स",
        ask_on_discord: "आमच्याकडे Discord वर विचारा",
        feedback: "प्रतिक्रिया",
        no_changes: "कोणतेही बदल नाहीत",
        loading: "लोड होत आहे...",
        last_saved: "शेवटचा जतन",
        saving: "जतन होत आहे...",
        failed_to_save: "जतन करण्यात अयशस्वी",
        fit_window_reset: "विंडो फिट करा / रीसेट करा",
        zoom: "झूम",
        add_table: "टेबल जोडा",
        add_area: "क्षेत्र जोडा",
        add_note: "नोंद जोडा",
        add_type: "प्रकार जोडा",
        to_do: "करण्यासाठी",
        tables: "टेबल्स",
        relationships: "संबंध",
        subject_areas: "विषय क्षेत्रे",
        notes: "नोंदी",
        types: "प्रकार",
        search: "शोधा...",
        no_tables: "कोणतेही टेबल्स नाहीत",
        no_tables_text: "आपला डायग्राम तयार करण्यास प्रारंभ करा!",
        no_relationships: "कोणतेही संबंध नाहीत",
        no_relationships_text: "फील्ड्स कनेक्ट करण्यासाठी खेचा आणि संबंध तयार करा!",
        no_subject_areas: "कोणतेही विषय क्षेत्रे नाहीत",
        no_subject_areas_text: "टेबल्स ग्रुप करण्यासाठी विषय क्षेत्रे जोडा!",
        no_notes: "कोणतेही नोट्स नाहीत",
        no_notes_text: "अतिरिक्त माहिती नोंदवण्यासाठी नोट्स वापरा",
        no_types: "कोणतेही प्रकार नाहीत",
        no_types_text: "आपले स्वतःचे कस्टम डेटा प्रकार तयार करा",
        no_issues: "कोणत्याही समस्या आढळल्या नाहीत.",
        strict_mode_is_on_no_issues:
            "कडक मोड बंद आहे म्हणून कोणत्याही समस्या दाखवल्या जाणार नाहीत.",
        name: "नाव",
        type: "प्रकार",
        null: "Null",
        not_null: "नॉट null",
        primary: "प्राथमिक",
        unique: "युनिक",
        autoincrement: "स्वतः वाढ",
        default_value: "डिफ़ॉल्ट",
        check: "चेक एक्सप्रेशन",
        this_will_appear_as_is: "*हे जेनरेटेड स्क्रिप्टमध्ये जसे आहे तसेच दिसेल.",
        comment: "टिप्पणी",
        add_field: "फील्ड जोडा",
        values: "व्हॅल्यूज",
        size: "आकार",
        precision: "तपशीलता",
        set_precision: "तपशीलता सेट करा: (आकार, डिजिट्स)",
        use_for_batch_input: "बॅच इनपुटसाठी वापरा",
        indices: "इंडायसेस",
        add_index: "इंडेक्स जोडा",
        select_fields: "फील्ड्स निवडा",
        title: "शीर्षक",
        not_set: "सेट नाही",
        foreign: "फॉरेन",
        cardinality: "कार्डिनॅलिटी",
        on_update: "अपडेट वर",
        on_delete: "हटवताना",
        swap: "स्वॅप",
        one_to_one: "एक ते एक",
        one_to_many: "एक ते अनेक",
        many_to_one: "अनेक ते एक",
        content: "कंटेंट",
        types_info:
            "ही सुविधा object-relational DBMS जसे PostgreSQL साठी आहे.\nMySQL किंवा MariaDB साठी वापरल्यास, एक JSON प्रकार जेनरेट केला जाईल ज्यात संबंधित json वैधता तपासणी असेल.\nSQLite साठी वापरल्यास ते BLOB मध्ये रूपांतरित केले जाईल.\nMSSQL साठी वापरल्यास प्रथम फील्डसाठी एक प्रकार उपनाम जेनरेट केला जाईल.",
        table_deleted: "टेबल हटवले",
        area_deleted: "क्षेत्र हटवले",
        note_deleted: "नोंद हटवली",
        relationship_deleted: "संबंध हटवला",
        type_deleted: "प्रकार हटवला",
        cannot_connect: "कनेक्ट करू शकत नाही, कॉलमचे प्रकार वेगवेगळे आहेत",
        copied_to_clipboard: "क्लिपबोर्डवर कॉपी केले",
        create_new_diagram: "नवीन डायग्राम तयार करा",
        cancel: "रद्द करा",
        open_diagram: "डायग्राम उघडा",
        rename_diagram: "डायग्रामचे नाव बदला",
        export: "निर्यात",
        export_image: "इमेज निर्यात",
        create: "तयार करा",
        confirm: "पुष्टी करा",
        last_modified: "शेवटचे बदलले",
        drag_and_drop_files:
            "फाइल येथे खेचा आणि सोडा किंवा अपलोड करण्यासाठी क्लिक करा.",
        upload_sql_to_generate_diagrams:
            "आपल्या टेबल आणि कॉलम स्वयंचलितपणे जेनरेट करण्यासाठी SQL फाइल अपलोड करा.",
        overwrite_existing_diagram: "मौजूदा डायग्राम अधिलेखित करा",
        only_mysql_supported: "*सध्या फक्त MySQL स्क्रिप्ट्स लोड करणे समर्थित आहे.",
        blank: "रिक्त",
        filename: "फाइलनाम",
        table_w_no_name: "नाव नसलेल्या टेबलची घोषणा केली",
        duplicate_table_by_name: "नावाने डुप्लिकेट टेबल '{{tableName}}'",
        empty_field_name: "टेबल '{{tableName}}' मध्ये रिक्त फील्ड 'नाव'",
        empty_field_type: "टेबल '{{tableName}}' मध्ये रिक्त फील्ड 'प्रकार'",
        no_values_for_field:
            "टेबल '{{tableName}}' च्या फील्ड '{{fieldName}}' चा प्रकार '{{type}}' आहे पण कोणतीही व्हॅल्यू निर्दिष्ट केलेली नाही",
        default_doesnt_match_type:
            "टेबल '{{tableName}}' च्या फील्ड '{{fieldName}}' ची डिफ़ॉल्ट व्हॅल्यू त्याच्या प्रकाराशी जुळत नाही",
        not_null_is_null:
            "टेबल '{{tableName}}' च्या फील्ड '{{fieldName}}' ची व्हॅल्यू NOT NULL आहे पण डिफ़ॉल्ट NULL आहे",
        duplicate_fields:
            "टेबल '{{tableName}}' मध्ये नाव '{{fieldName}}' असलेले डुप्लिकेट टेबल फील्ड्स",
        duplicate_index:
            "टेबल '{{tableName}}' मध्ये नाव '{{indexName}}' असलेला डुप्लिकेट इंडेक्स",
        empty_index:
            "टेबल '{{tableName}}' मध्ये इंडेक्स कोणताही कॉलम इंडेक्स करत नाही",
        no_primary_key: "टेबल '{{tableName}}' मध्ये कोणतीही प्राथमिक कुंजी नाही",
        type_with_no_name: "नाव नसलेला प्रकार घोषित केला",
        duplicate_types: "नाव '{{typeName}}' असलेले डुप्लिकेट प्रकार",
        type_w_no_fields: "कोणतेही फील्ड नसलेला प्रकार '{{typeName}}' घोषित केला",
        empty_type_field_name: "प्रकार '{{typeName}}' मध्ये रिक्त फील्ड 'नाव'",
        empty_type_field_type: "प्रकार '{{typeName}}' मध्ये रिक्त फील्ड 'प्रकार'",
        no_values_for_type_field:
            "प्रकार '{{typeName}}' च्या फील्ड '{{fieldName}}' चा प्रकार '{{type}}' आहे पण कोणतीही व्हॅल्यू निर्दिष्ट केलेली नाही",
        duplicate_type_fields:
            "प्रकार '{{typeName}}' मध्ये नाव '{{fieldName}}' असलेले डुप्लिकेट प्रकार फील्ड्स",
        duplicate_reference: "नाव '{{refName}}' असलेला डुप्लिकेट संदर्भ",
        circular_dependency: "टेबल '{{refName}}' मध्ये परिपत्रक अवलंबित्व",
        timeline: "वेळरेखा",
        priority: "प्राथमिकता",
        none: "कोणतीही नाही",
        low: "कमी",
        medium: "मध्यम",
        high: "उच्च",
        sort_by: "क्रमानुसार",
        my_order: "माझा क्रम",
        completed: "पूर्ण",
        alphabetically: "वर्णक्रमानुसार",
        add_task: "कार्य जोडा",
        details: "तपशील",
        no_tasks: "आपल्याकडे अद्याप कोणतीही कार्ये नाहीत.",
        no_activity: "आपल्याकडे अद्याप कोणतीही क्रियाकलाप नाही.",
        move_element: "{{name}} ला {{coords}} वर हलवा",
        edit_area: "{{extra}} संपादन क्षेत्र {{areaName}}",
        delete_area: "क्षेत्र हटवा {{areaName}}",
        edit_note: "{{extra}} नोंद संपादन करा {{noteTitle}}",
        delete_note: "नोंद हटवा {{noteTitle}}",
        edit_table: "{{extra}} टेबल संपादन करा {{tableName}}",
        delete_table: "टेबल हटवा {{tableName}}",
        edit_type: "{{extra}} प्रकार संपादन करा {{typeName}}",
        delete_type: "प्रकार हटवा {{typeName}}",
        add_relationship: "संबंध जोडा",
        edit_relationship: "{{extra}} संबंध संपादन करा {{refName}}",
        delete_relationship: "संबंध हटवा {{refName}}",
        not_found: "सापडले नाही",
    },
};

export {mr, marathi};
