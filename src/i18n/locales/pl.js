const polish = {
    name: "Polish",
    native_name: "<PERSON><PERSON>",
    code: "pl",
};

const pl = {
    translation: {
        report_bug: "<PERSON>g<PERSON><PERSON><PERSON> błąd",
        import_from: "Importuj",
        import: "Importuj",
        file: "Plik",
        new: "<PERSON><PERSON>",
        new_window: "Nowe okno",
        open: "Otw<PERSON><PERSON>",
        save: "<PERSON><PERSON><PERSON><PERSON>",
        save_as: "Zapisz jako",
        save_as_template: "Zapisz jako szablon",
        template_saved: "Szablon zapisany!",
        rename: "<PERSON>mie<PERSON> nazwę",
        delete_diagram: "<PERSON>u<PERSON> diagram",
        are_you_sure_delete_diagram:
            "<PERSON><PERSON> na pewno chcesz usunąć ten diagram? Ta operacja jest nieodwracalna.",
        oops_smth_went_wrong: "Ups! Coś poszło nie tak.",
        import_diagram: "Importuj diagram",
        import_from_source: "Importuj z SQL",
        export_as: "Eksportuj jako",
        export_source: "Eksportuj SQL",
        models: "<PERSON>e",
        exit: "<PERSON><PERSON>j<PERSON><PERSON>",
        edit: "<PERSON><PERSON>u<PERSON>",
        undo: "Cofnij",
        redo: "Ponów",
        clear: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        are_you_sure_clear:
            "<PERSON>zy na pewno chcesz wyczyścić diagram? To jest nieodwracalne.",
        cut: "Wytnij",
        copy: "Kopiuj",
        paste: "Wklej",
        duplicate: "Duplikuj",
        delete: "Usuń",
        copy_as_image: "Kopiuj jako obraz",
        view: "Widok",
        header: "Pasek menu",
        sidebar: "Pasek boczny",
        issues: "Problemy",
        presentation_mode: "Tryb prezentacji",
        strict_mode: "Tryb ścisły",
        field_details: "Szczegóły pola",
        reset_view: "Resetuj widok",
        show_grid: "Pokaż siatkę",
        show_cardinality: "Pokaż krotność",
        theme: "Motyw",
        light: "Jasny",
        dark: "Ciemny",
        zoom_in: "Powiększ",
        zoom_out: "Pomniejsz",
        fullscreen: "Pełny ekran",
        settings: "Ustawienia",
        show_timeline: "Pokaż oś czasu",
        autosave: "Automatyczne zapisywanie",
        panning: "Przesuwanie",
        show_debug_coordinates: "Pokaż współrzędne debugowania",
        transform: "Przekształć",
        viewbox: "Obszar widoku",
        cursor_coordinates: "Współrzędne kursora",
        coordinate_space: "Przestrzeń",
        coordinate_space_screen: "Ekran",
        coordinate_space_diagram: "Diagram",
        table_width: "Szerokość tabeli",
        language: "Język",
        flush_storage: "Wyczyść pamięć",
        are_you_sure_flush_storage:
            "Czy na pewno chcesz wyczyścić pamięć? To nieodwracalnie usunie wszystkie twoje diagramy i własne szablony.",
        storage_flushed: "Pamięć wyczyszczona",
        help: "Pomoc",
        shortcuts: "Skróty",
        ask_on_discord: "Zapytaj nas na Discordzie",
        feedback: "Opinie",
        no_changes: "Brak zmian",
        loading: "Ładowanie...",
        last_saved: "Ostatnio zapisane",
        saving: "Zapisywanie...",
        failed_to_save: "Nie udało się zapisać",
        fit_window_reset: "Dopasuj okno / Resetuj",
        zoom: "Powiększenie",
        add_table: "Dodaj tabelę",
        add_area: "Dodaj obszar",
        add_note: "Dodaj notatkę",
        add_type: "Dodaj typ",
        to_do: "Do zrobienia",
        tables: "Tabele",
        relationships: "Relacje",
        subject_areas: "Obszary tematyczne",
        notes: "Notatki",
        types: "Typy",
        search: "Szukaj...",
        no_tables: "Brak tabel",
        no_tables_text: "Zacznij budować swój diagram!",
        no_relationships: "Brak relacji",
        no_relationships_text: "Przeciągnij, aby połączyć pola i tworzyć relacje!",
        no_subject_areas: "Brak obszarów tematycznych",
        no_subject_areas_text: "Dodaj obszary tematyczne, aby grupować tabele!",
        no_notes: "Brak notatek",
        no_notes_text: "Używaj notatek do zapisywania dodatkowych informacji",
        no_types: "Brak typów",
        no_types_text: "Twórz własne typy danych",
        no_issues: "Nie wykryto żadnych problemów.",
        strict_mode_is_on_no_issues:
            "Tryb ścisły jest wyłączony, więc żadne problemy nie zostaną wyświetlone.",
        name: "Nazwa",
        type: "Typ",
        null: "Null",
        not_null: "Nie null",
        primary: "Klucz główny",
        unique: "Unikalny",
        autoincrement: "Autonumerowanie",
        default_value: "Wartość domyślna",
        check: "Wyrażenie sprawdzające",
        this_will_appear_as_is:
            "*To pojawi się w wygenerowanym skrypcie tak, jak jest.",
        comment: "Komentarz",
        add_field: "Dodaj pole",
        values: "Wartości",
        size: "Rozmiar",
        precision: "Precyzja",
        set_precision: "Ustaw precyzję: 'rozmiar, cyfry'",
        use_for_batch_input: "Użyj , do wsadowego wprowadzania",
        indices: "Indeksy",
        add_index: "Dodaj indeks",
        select_fields: "Wybierz pola",
        title: "Tytuł",
        not_set: "Nie ustawiono",
        foreign: "Obcy",
        cardinality: "Krotność",
        on_update: "Przy aktualizacji",
        on_delete: "Przy usunięciu",
        swap: "Zamień",
        one_to_one: "Jeden do jednego",
        one_to_many: "Jeden do wielu",
        many_to_one: "Wiele do jednego",
        content: "Zawartość",
        types_info:
            "Ta funkcja jest przeznaczona dla obiektowo-relacyjnych systemów baz danych, takich jak PostgreSQL.\nJeśli używana dla MySQL lub MariaDB, zostanie wygenerowany typ JSON z odpowiadającym mu sprawdzeniem walidacji JSON.\nJeśli używana dla SQLite, zostanie przetłumaczona na BLOB.\nJeśli używana dla MSSQL, zostanie wygenerowany alias typu do pierwszego pola.",
        table_deleted: "Tabela usunięta",
        area_deleted: "Obszar usunięty",
        note_deleted: "Notatka usunięta",
        relationship_deleted: "Relacja usunięta",
        type_deleted: "Typ usunięty",
        cannot_connect: "Nie można połączyć, kolumny mają różne typy",
        copied_to_clipboard: "Skopiowano do schowka",
        create_new_diagram: "Utwórz nowy diagram",
        cancel: "Anuluj",
        open_diagram: "Otwórz diagram",
        rename_diagram: "Zmień nazwę diagramu",
        export: "Eksportuj",
        export_image: "Eksportuj obraz",
        create: "Utwórz",
        confirm: "Potwierdź",
        last_modified: "Ostatnio zmodyfikowano",
        drag_and_drop_files:
            "Przeciągnij i upuść plik tutaj lub kliknij, aby przesłać.",
        upload_sql_to_generate_diagrams:
            "Prześlij plik SQL, aby automatycznie wygenerować tabele i kolumny.",
        overwrite_existing_diagram: "Nadpisz istniejący diagram",
        only_mysql_supported:
            "*Obecnie obsługiwane jest tylko ładowanie skryptów MySQL.",
        blank: "Pusty",
        filename: "Nazwa pliku",
        table_w_no_name: "Zadeklarowano tabelę bez nazwy",
        duplicate_table_by_name: "Duplikat tabeli o nazwie '{{tableName}}'",
        empty_field_name: "Puste pole `name` w tabeli '{{tableName}}'",
        empty_field_type: "Puste pole `type` w tabeli '{{tableName}}'",
        no_values_for_field:
            "Pole '{{fieldName}}' w tabeli '{{tableName}}' jest typu `{{type}}`, ale nie określono wartości",
        default_doesnt_match_type:
            "Wartość domyślna dla pola '{{fieldName}}' w tabeli '{{tableName}}' nie pasuje do jego typu",
        not_null_is_null:
            "Pole '{{fieldName}}' w tabeli '{{tableName}}' jest NOT NULL, ale ma wartość domyślną NULL",
        duplicate_fields:
            "Duplikat pól tabeli o nazwie '{{fieldName}}' w tabeli '{{tableName}}'",
        duplicate_index:
            "Duplikat indeksu o nazwie '{{indexName}}' w tabeli '{{tableName}}'",
        empty_index: "Indeks w tabeli '{{tableName}}' nie indeksuje żadnych kolumn",
        no_primary_key: "Tabela '{{tableName}}' nie ma klucza głównego",
        type_with_no_name: "Zadeklarowano typ bez nazwy",
        duplicate_types: "Duplikat typów o nazwie '{{typeName}}'",
        type_w_no_fields: "Zadeklarowano pusty typ '{{typeName}}' bez pól",
        empty_type_field_name: "Puste pole `name` w typie '{{typeName}}'",
        empty_type_field_type: "Puste pole `type` w typie '{{typeName}}'",
        no_values_for_type_field:
            "Pole '{{fieldName}}' w typie '{{typeName}}' jest typu `{{type}}`, ale nie określono wartości",
        duplicate_type_fields:
            "Duplikat pól typu o nazwie '{{fieldName}}' w typie '{{typeName}}'",
        duplicate_reference: "Duplikat referencji o nazwie '{{refName}}'",
        circular_dependency: "Cykliczna zależność obejmująca tabelę '{{refName}}'",
        timeline: "Oś czasu",
        priority: "Priorytet",
        none: "Brak",
        low: "Niski",
        medium: "Średni",
        high: "Wysoki",
        sort_by: "Sortuj według",
        my_order: "Moja kolejność",
        completed: "Ukończone",
        alphabetically: "Alfabetycznie",
        add_task: "Dodaj zadanie",
        details: "Szczegóły",
        no_tasks: "Nie masz jeszcze żadnych zadań.",
        no_activity: "Nie masz jeszcze żadnej aktywności.",
        move_element: "Przenieś {{name}} do {{coords}}",
        edit_area: "{{extra}} Edytuj obszar {{areaName}}",
        delete_area: "Usuń obszar {{areaName}}",
        edit_note: "{{extra}} Edytuj notatkę {{noteTitle}}",
        delete_note: "Usuń notatkę {{noteTitle}}",
        edit_table: "{{extra}} Edytuj tabelę {{tableName}}",
        delete_table: "Usuń tabelę {{tableName}}",
        edit_type: "{{extra}} Edytuj typ {{typeName}}",
        delete_type: "Usuń typ {{typeName}}",
        add_relationship: "Dodaj relację",
        edit_relationship: "{{extra}} Edytuj relację {{refName}}",
        delete_relationship: "Usuń relację {{refName}}",
        not_found: "Nie znaleziono",
        pick_db: "Wybierz bazę danych",
        generic: "Ogólny",
        generic_description:
            "Ogólne diagramy mogą być eksportowane do dowolnego dialektu SQL, ale obsługują niewiele typów danych.",
        enums: "Wyliczenia",
        add_enum: "Dodaj wyliczenie",
        edit_enum: "{{extra}} Edytuj wyliczenie {{enumName}}",
        delete_enum: "Usuń wyliczenie",
        enum_w_no_name: "Znaleziono wyliczenie bez nazwy",
        enum_w_no_values: "Znaleziono wyliczenie '{{enumName}}' bez wartości",
        duplicate_enums: "Duplikat wyliczeń o nazwie '{{enumName}}'",
        no_enums: "Brak wyliczeń",
        no_enums_text: "Zdefiniuj wyliczenia tutaj",
        declare_array: "Zadeklaruj tablicę",
        empty_index_name: "Zadeklarowano indeks bez nazwy w tabeli '{{tableName}}'",
        didnt_find_diagram: "Ups! Nie znaleziono diagramu.",
        unsigned: "Bez znaku",
        share: "Udostępnij",
        copy_link: "Kopiuj link",
        readme: "README",
        failed_to_load:
            "Nie udało się załadować. Upewnij się, że link jest poprawny.",
        share_info:
            "* Udostępnienie tego linku nie utworzy sesji współpracy w czasie rzeczywistym.",
    },
};

export {pl, polish};
