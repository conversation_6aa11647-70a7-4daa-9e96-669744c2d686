const hungarian = {
    name: "Hungarian",
    native_name: "<PERSON><PERSON><PERSON>",
    code: "hu",
};

const hu = {
    translation: {
        report_bug: "Hiba jelentése",
        import: "Import<PERSON>l<PERSON>",
        import_from: "Import<PERSON><PERSON><PERSON> innen",
        file: "Fájl",
        new: "Új",
        new_window: "Új ablak",
        open: "Megnyitás",
        save: "Mentés",
        save_as: "Mentés másként",
        save_as_template: "Mentés sablonként",
        template_saved: "Sablon elmentve!",
        rename: "Átnevezés",
        delete_diagram: "Diagram törlése",
        are_you_sure_delete_diagram:
            "Biztosan törli ezt a diagramot? Ez a művelet végleges.",
        oops_smth_went_wrong: "Hoppá! Valami hiba történt.",
        import_diagram: "Diagram importálása",
        import_from_source: "Importálás SQL-ből",
        export_as: "Exportálás mint",
        export_source: "<PERSON>rtálás SQL-be",
        models: "<PERSON><PERSON><PERSON>",
        exit: "Kilépés",
        edit: "<PERSON>zerkesztés",
        undo: "Visszavonás",
        redo: "Ismétlés",
        clear: "Kitörlés",
        are_you_sure_clear:
            "Biztosan törli a diagram tartalmát? Ez a művelet végleges.",
        cut: "Kivágás",
        copy: "Másolás",
        paste: "Beillesztés",
        duplicate: "Duplikálás",
        delete: "Törlés",
        copy_as_image: "Másolás képként",
        view: "Nézet",
        header: "Menüsáv",
        sidebar: "Oldalsáv",
        issues: "Problémák",
        presentation_mode: "Prezentációs mód",
        strict_mode: "Szigorú mód",
        field_details: "Mezők részletei",
        reset_view: "Nézet alaphelyzetbe",
        show_grid: "Rács",
        snap_to_grid: "Rácshoz igazítás",
        show_datatype: "Adattípus",
        show_cardinality: "Kardinalitás",
        theme: "Téma",
        light: "Világos",
        dark: "Sötét",
        zoom_in: "Nagyítás",
        zoom_out: "Kicsinyítés",
        fullscreen: "Teljes képernyő",
        settings: "Beállítások",
        show_timeline: "Szerkesztési előzmények",
        autosave: "Automatikus mentés",
        panning: "Mozgatás",
        show_debug_coordinates: "Hibakeresési koordináták",
        transform: "Elhelyezkedés",
        viewbox: "Szerkesztő",
        cursor_coordinates: "Kurzor koordinátái",
        coordinate_space: "Tér",
        coordinate_space_screen: "Szerkesztő",
        coordinate_space_diagram: "Diagram",
        table_width: "Táblák szélessége",
        language: "Nyelv",
        flush_storage: "Tárhely ürítése",
        are_you_sure_flush_storage:
            "Biztosan üríti a tárhelyet? Ez véglegesen törölni fogja minden diagramját és egyedi sablonját.",
        storage_flushed: "A tárhely kiürítve",
        help: "Súgó",
        shortcuts: "Gyorsbillentyűk",
        ask_on_discord: "Érdeklődjön Discord-on",
        feedback: "Visszajelzés",
        no_changes: "Nem volt módosítva",
        loading: "Betöltés...",
        last_saved: "Utoljára mentve:",
        saving: "Mentés...",
        failed_to_save: "A mentés sikertelen",
        fit_window_reset: "Ablakhoz illesztés",
        zoom: "Nagyítás",
        add_table: "Új tábla",
        add_area: "Új terület",
        add_note: "Új jegyzet",
        add_type: "Új típus",
        to_do: "Teendők",
        tables: "Táblák",
        relationships: "Kapcsolatok",
        subject_areas: "Területek",
        notes: "Jegyzetek",
        types: "Típusok",
        search: "Keresés...",
        no_tables: "Nincs tábla",
        no_tables_text: "Vágjon bele diagramja elkészítésébe!",
        no_relationships: "Nincs kapcsolat létrehozva",
        no_relationships_text:
            "Egérhúzással összeköthet mezőket kapcsolatok létrehozásához",
        no_subject_areas: "Nincs terület hozzáadva",
        no_subject_areas_text:
            "Adjon hozzá területeket a táblák csoportosításához!",
        no_notes: "Nincs jegyzet",
        no_notes_text: "Használjon jegyzeteket további információk feljegyzéséhez",
        no_types: "Nincs típus",
        no_types_text: "Készítsen egyedi adattípusokat",
        no_issues: "Nem észleltünk problémát.",
        strict_mode_is_on_no_issues:
            "A szigorú mód le van tiltva, így a problémák nem fognak megjelenni.",
        name: "Név",
        type: "Típus",
        null: "Null",
        not_null: "Nem null",
        nullable: "Nullázható",
        primary: "Elsődleges",
        unique: "Egyedi",
        autoincrement: "Automatikus növelés",
        default_value: "Alapért.",
        check: "'Check' kifejezés",
        this_will_appear_as_is:
            "*Ez a generált parancsfájlban ugyanígy fog megjelenni.",
        comment: "Megjegyzés",
        add_field: "Új mező",
        values: "Értékek",
        size: "Méret",
        precision: "Pontosság",
        set_precision: "Pontosság: 'méret, számjegyek'",
        use_for_batch_input: "Több értéket vesszővel elválasztva adhat meg",
        indices: "Indexek",
        add_index: "Új index",
        select_fields: "Mezők kiválasztása",
        title: "Cím",
        not_set: "Nincs megadva",
        foreign: "Idegen",
        cardinality: "Kardinalitás",
        on_update: "Frissítéskor",
        on_delete: "Törléskor",
        swap: "Felcserélés",
        one_to_one: "Egy-az-egyhez",
        one_to_many: "Egy-a-többhöz",
        many_to_one: "Több-az-egyhez",
        content: "Tartalom",
        types_info:
            "Ez a funkció objektum-relációs adatbázisokhoz van tervezve, mint például a PostgreSQL.\nHa MySQL-hez vagy MariaDB-hez van használva, egy JSON típus lesz generálva a megfelelő json validációval.\nSQLite esetén BLOB típusként fog megjelenni.\nMSSQL esetén pedig egy alternatív típusnév lesz generálva az első mezőhöz.",
        table_deleted: "A tábla törölve",
        area_deleted: "A terület törölve",
        note_deleted: "A jegyzet törölve",
        relationship_deleted: "A kapcsolat törölve",
        type_deleted: "A típus törölve",
        cannot_connect:
            "A kapcsolatot nem lehet létrehozni, az oszlopok eltérő típusúak",
        copied_to_clipboard: "Másolva a vágólapra",
        create_new_diagram: "Új diagram létrehozása",
        cancel: "Mégse",
        open_diagram: "Diagram megnyitása",
        rename_diagram: "Diagram átnevezése",
        export: "Exportálás",
        export_image: "Kép exportálása",
        create: "Létrehozás",
        confirm: "Megerősítés",
        last_modified: "Utoljára módosítva",
        drag_and_drop_files:
            "Húzzon ide egy fájlt vagy kattintson a feltöltendő fájl kiválasztásához.",
        upload_sql_to_generate_diagrams:
            "Töltsön fel egy sql fájlt a táblák és oszlopok automatikus generálásához.",
        overwrite_existing_diagram: "Meglévő diagram felülírása",
        only_mysql_supported: "*Jelenleg csak MySQL fájlok betöltése támogatott.",
        blank: "Üres",
        filename: "Fájlnév",
        table_w_no_name: "Nincs megadva név egy táblának",
        duplicate_table_by_name:
            "Több tábla is létezik ezzel a névvel: '{{tableName}}'",
        empty_field_name:
            "Nincs megadva egy mező `neve` a(z) '{{tableName}}' táblában",
        empty_field_type:
            "Nincs megadva egy mező `típusa` a(z) '{{tableName}}' táblában",
        no_values_for_field:
            "A(z) '{{tableName}}' tábla '{{fieldName}}' mezője `{{type}}` típusú, de nincs megadva érték",
        default_doesnt_match_type:
            "A(z) '{{tableName}}' tábla '{{fieldName}}' mezőjének alapértelmezett értéke nem egyezik a mező típusával",
        not_null_is_null:
            "A(z) '{{tableName}}' tábla '{{fieldName}}' mezője NOT NULL-ként van beállítva, de a megadott alapért. érték NULL",
        duplicate_fields:
            "Több '{{fieldName}}' nevű mező is létezik a(z) '{{tableName}}' táblában",
        duplicate_index:
            "Több '{{indexName}}' nevű index is létezik a(z) '{{tableName}}' táblában",
        empty_index:
            "A(z) '{{tableName}}' táblában levő index egyetlen oszlopot sem indexel",
        no_primary_key:
            "A(z) '{{tableName}}' tábla nem rendelkezik elsődleges kulccsal",
        type_with_no_name: "Nincs megadva egy egyedi típus neve",
        duplicate_types: "Több típus is létezik a(z) '{{typeName}}' névvel",
        type_w_no_fields: "Nincs megadva mező a(z) '{{typeName}}' típusnak",
        empty_type_field_name: "Nincs megadva a(z) '{{typeName}}' típus `neve`",
        empty_type_field_type: "Nincs megadva a(z) '{{typeName}}' típus `típusa`",
        no_values_for_type_field:
            "A(z) '{{typeName}}' típus '{{fieldName}}' mezője `{{type}}` típusú, de nincs megadva érték",
        duplicate_type_fields:
            "Több '{{fieldName}}' nevű mező is létezik a(z) '{{typeName}}' típussal",
        duplicate_reference: "Több hivatkozás is létezik a(z) '{{refName}}' névvel",
        circular_dependency:
            "Körkörös hivatkozás a(z) '{{refName}}' táblával kapcsolatban",
        timeline: "Előzmények",
        priority: "Prioritás",
        none: "Nincs",
        low: "Alacsony",
        medium: "Közepes",
        high: "Magas",
        sort_by: "Rendezés",
        my_order: "Saját sorrend",
        completed: "Befejezetlenek elöl",
        alphabetically: "ABC sorrend",
        add_task: "Új feladat",
        details: "Részletek",
        no_tasks: "Nincs teendő.",
        no_activity: "Még nem történt módosítás.",
        move_element: "{{name}} mozgatása a {{coords}} koordinátákra",
        edit_area: "{{extra}} {{areaName}} terület szerkesztése",
        delete_area: "{{areaName}} terület törlése",
        edit_note: "{{extra}} {{noteTitle}} jegyzet szerkesztése",
        delete_note: "{{noteTitle}} jegyzet törlése",
        edit_table: "{{extra}} {{tableName}} tábla szerkesztése",
        delete_table: "{{tableName}} tábla törlése",
        edit_type: "{{extra}} {{typeName}} típus szerkesztése",
        delete_type: "{{typeName}} típus törlése",
        add_relationship: "Új kapcsolat",
        edit_relationship: "{{extra}} {{refName}} kapcsolat szerkesztése",
        delete_relationship: "{{refName}} kapcsolat törlése",
        not_found: "Nincs találat",
        pick_db: "Válasszon adatbázist",
        generic: "Általános",
        generic_description:
            "Az általános diagramok bármilyen fajta SQL-be exportálhatóak, de kevés adattípust támogatnak.",
        enums: "Enum-ok",
        add_enum: "Új enum",
        edit_enum: "{{extra}} {{enumName}} enum szerkesztése",
        delete_enum: "Enum törlése",
        enum_w_no_name: "Egy enum-nak nincs megadva név",
        enum_w_no_values: "A(z) '{{enumName}}' enum-ban nincs megadva érték",
        duplicate_enums: "Több enum is létezik a(z) '{{enumName}}' névvel",
        no_enums: "Nincs enum",
        no_enums_text: "Itt adhat meg enum-okat",
        declare_array: "Tömb megadása",
        empty_index_name:
            "A(z) '{{tableName}}' táblában egy indexnek nincs megadva név",
        didnt_find_diagram: "Hoppá! A diagram nem található.",
        unsigned: "Előjel nélküli",
        share: "Megosztás",
        unshare: "Megosztás megszüntetése",
        copy_link: "Hivatkozás másolása",
        readme: "OLVASS_EL",
        failed_to_load:
            "A betöltés sikertelen. Ellenőrizze a hivatkozás helyességét!",
        share_info:
            "* Ezen hivatkozás megosztása nem fog létrehozni élő, valósidejű együttműködési munkamenetet.",
        show_relationship_labels: "Kapcsolatcímkék",
        docs: "Dokumentáció",
        supported_types: "Támogatott fájltípusok:",
        bulk_update: "Csoportos frissítés",
        multiselect: "Csoportos kijelölés",
        export_saved_data: "Mentett adatok exportálása",
        dbml_view: "DBML nézet",
        tab_view: "Lap nézet",
    },
};

export {hu, hungarian};
