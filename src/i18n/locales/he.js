const hebrew = {
    name: "Hebrew",
    native_name: "עברי<PERSON>",
    code: "he",
};

const he = {
    translation: {
        report_bug: "דווח על באג",
        import_from: "ייבוא",
        import: "ייבו<PERSON>",
        file: "קובץ",
        new: "חדש",
        new_window: "חלון חדש",
        open: "פתח",
        save: "שמור",
        save_as: "שמור בשם",
        save_as_template: "שמור כתבנית",
        template_saved: "תבנית נשמרה!",
        rename: "שנה שם",
        delete_diagram: "מחק דיאגרמה",
        are_you_sure_delete_diagram:
            "האם אתה בטוח שברצונך למחוק דיאגרמה זו? פעולה זו אינה ניתנת לביטול.",
        oops_smth_went_wrong: "אופס! משהו השתבש.",
        import_diagram: "ייבא דיאגרמה",
        import_from_source: "ייבוא מ-S<PERSON>",
        export_as: "ייצוא כ",
        export_source: "ייצ<PERSON> SQL",
        models: "מודלים",
        exit: "יציאה",
        edit: "ערוך",
        undo: "בטל",
        redo: "בצע שוב",
        clear: "נקה",
        are_you_sure_clear:
            "האם אתה בטוח שברצונך לנקות את הדיאגרמה? פעולה זו אינה ניתנת לביטול.",
        cut: "גזור",
        copy: "העתק",
        paste: "הדבק",
        duplicate: "שכפל",
        delete: "מחק",
        copy_as_image: "העתק כתמונה",
        view: "הצג",
        header: "תפריט עליון",
        sidebar: "תפריט צדדי",
        issues: "בעיות",
        presentation_mode: "מצב מצגת",
        strict_mode: "מצב קפדני",
        field_details: "פרטי שדה",
        reset_view: "אפס תצוגה",
        show_grid: "הצג רשת",
        show_cardinality: "הצג קרדינליות",
        theme: "ערכת נושא",
        light: "בהיר",
        dark: "כהה",
        zoom_in: "הגדל",
        zoom_out: "הקטן",
        fullscreen: "מסך מלא",
        settings: "הגדרות",
        show_timeline: "הצג ציר זמן",
        autosave: "שמירה אוטומטית",
        panning: "הזזה",
        show_debug_coordinates: "הצג קואורדינטות ניפוי באגים",
        transform: "המר",
        viewbox: "תיבת תצוגה",
        cursor_coordinates: "קואורדינטות סמן",
        coordinate_space: "מרחב",
        coordinate_space_screen: "מסך",
        coordinate_space_diagram: "דיאגרמה",
        table_width: "רוחב טבלה",
        language: "שפה",
        flush_storage: "נקה אחסון",
        are_you_sure_flush_storage:
            "האם אתה בטוח שברצונך לנקות את האחסון? פעולה זו תמחק באופן בלתי הפיך את כל הדיאגרמות והתבניות המותאמות אישית שלך.",
        storage_flushed: "אחסון נוקה",
        help: "עזרה",
        shortcuts: "קיצורי דרך",
        ask_on_discord: "שאל אותנו ב-Discord",
        feedback: "משוב",
        no_changes: "אין שינויים",
        loading: "טוען...",
        last_saved: "נשמר לאחרונה",
        saving: "שומר...",
        failed_to_save: "שמירה נכשלה",
        fit_window_reset: "התאם חלון / איפוס",
        zoom: "זום",
        add_table: "הוסף טבלה",
        add_area: "הוסף אזור",
        add_note: "הוסף הערה",
        add_type: "הוסף סוג",
        to_do: "לביצוע",
        tables: "טבלאות",
        relationships: "קשרים",
        subject_areas: "תחומי עניין",
        notes: "הערות",
        types: "סוגים",
        search: "חיפוש...",
        no_tables: "אין טבלאות",
        no_tables_text: "התחל לבנות את הדיאגרמה שלך!",
        no_relationships: "אין קשרים",
        no_relationships_text: "גרור כדי לחבר שדות וליצור קשרים!",
        no_subject_areas: "אין תחומי עניין",
        no_subject_areas_text: "הוסף תחומי עניין כדי לקבץ טבלאות!",
        no_notes: "אין הערות",
        no_notes_text: "השתמש בהערות כדי לרשום מידע נוסף",
        no_types: "אין סוגים",
        no_types_text: "צור סוגי נתונים מותאמים אישית",
        no_issues: "לא נמצאו בעיות.",
        strict_mode_is_on_no_issues: "מצב קפדני כבוי ולכן לא יוצגו בעיות.",
        name: "שם",
        type: "סוג",
        null: "ריק",
        not_null: "לא ריק",
        primary: "ראשי",
        unique: "ייחודי",
        autoincrement: "הגדלה אוטומטית",
        default_value: "ברירת מחדל",
        check: "ביטוי בדיקה",
        this_will_appear_as_is: "*זה יופיע בסקריפט שנוצר כמו שהוא.",
        comment: "הערה",
        add_field: "הוסף שדה",
        values: "ערכים",
        size: "גודל",
        precision: "דיוק",
        set_precision: "הגדר דיוק: (גודל, ספרות)",
        use_for_batch_input: "השתמש ב-, להזנת קבוצות",
        indices: "אינדקסים",
        add_index: "הוסף אינדקס",
        select_fields: "בחר שדות",
        title: "כותרת",
        not_set: "לא מוגדר",
        foreign: "זר",
        cardinality: "קרדינליות",
        on_update: "בזמן עדכון",
        on_delete: "בזמן מחיקה",
        swap: "החלף",
        one_to_one: "אחד לאחד",
        one_to_many: "אחד לרבים",
        many_to_one: "רבים לאחד",
        content: "תוכן",
        types_info:
            "תכונה זו מיועדת למערכות ניהול נתונים אובייקט-רלציוניות כמו PostgreSQL.\nאם משמשת עבור MySQL או MariaDB תיווצר סוג JSON עם בדיקת תוקף json תואמת.\nאם משמשת עבור SQLite היא תתורגם ל-BLOB.\nאם משמשת עבור MSSQL תיווצר סוג סינונימי לשדה הראשון.",
        table_deleted: "טבלה נמחקה",
        area_deleted: "אזור נמחק",
        note_deleted: "הערה נמחקה",
        relationship_deleted: "קשר נמחק",
        type_deleted: "סוג נמחק",
        cannot_connect: "לא ניתן לחבר, העמודות הן מסוגים שונים",
        copied_to_clipboard: "הועתק ללוח",
        create_new_diagram: "צור דיאגרמה חדשה",
        cancel: "בטל",
        open_diagram: "פתח דיאגרמה",
        rename_diagram: "שנה שם לדיאגרמה",
        export: "ייצוא",
        export_image: "ייצא תמונה",
        create: "צור",
        confirm: "אשר",
        last_modified: "נערך לאחרונה",
        drag_and_drop_files: "גרור ושחרר את הקובץ כאן או לחץ להעלאה.",
        upload_sql_to_generate_diagrams:
            "העלה קובץ SQL כדי ליצור באופן אוטומטי את הטבלאות והעמודות שלך.",
        overwrite_existing_diagram: "דרוס דיאגרמה קיימת",
        only_mysql_supported: "*כרגע נתמך רק טעינת סקריפטים של MySQL.",
        blank: "ריק",
        filename: "שם קובץ",
        table_w_no_name: "הוכרז טבלה ללא שם",
        duplicate_table_by_name: "שכפול טבלה בשם '{{tableName}}'",
        empty_field_name: "שדה `name` ריק בטבלה '{{tableName}}'",
        empty_field_type: "שדה `type` ריק בטבלה '{{tableName}}'",
        no_values_for_field:
            "שדה '{{fieldName}}' בטבלה '{{tableName}}' הוא מסוג `{{type}}` אך לא הוגדרו ערכים",
        default_doesnt_match_type:
            "ערך ברירת מחדל עבור שדה '{{fieldName}}' בטבלה '{{tableName}}' אינו תואם לסוג שלו",
        not_null_is_null:
            "שדה '{{fieldName}}' בטבלה '{{tableName}}' אינו ריק אך ערך ברירת מחדל הוא NULL",
        duplicate_fields:
            "שדות טבלה כפולים בשם '{{fieldName}}' בטבלה '{{tableName}}'",
        duplicate_index: "אינדקס כפול בשם '{{indexName}}' בטבלה '{{tableName}}'",
        empty_index: "אינדקס בטבלה '{{tableName}}' אינו מאנדקס שום עמודות",
        no_primary_key: "לטבלה '{{tableName}}' אין מפתח ראשי",
        type_with_no_name: "הוכרז סוג ללא שם",
        duplicate_types: "סוגים כפולים בשם '{{typeName}}'",
        type_w_no_fields: "הוכרז סוג ריק '{{typeName}}' ללא שדות",
        empty_type_field_name: "שדה `name` ריק בסוג '{{typeName}}'",
        empty_type_field_type: "שדה `type` ריק בסוג '{{typeName}}'",
        no_values_for_type_field:
            "שדה '{{fieldName}}' בסוג '{{typeName}}' הוא מסוג `{{type}}` אך לא הוגדרו ערכים",
        duplicate_type_fields:
            "שדות סוג כפולים בשם '{{fieldName}}' בסוג '{{typeName}}'",
        duplicate_reference: "הפניה כפולה בשם '{{refName}}'",
        circular_dependency: "תלות מעגלית בטבלה '{{refName}}'",
        timeline: "ציר זמן",
        priority: "עדיפות",
        none: "ללא",
        low: "נמוכה",
        medium: "בינונית",
        high: "גבוהה",
        sort_by: "מיין לפי",
        my_order: "הסדר שלי",
        completed: "הושלם",
        alphabetically: "בסדר אלפביתי",
        add_task: "הוסף משימה",
        details: "פרטים",
        no_tasks: "אין לך משימות עדיין.",
        no_activity: "אין לך פעילות עדיין.",
        move_element: "העבר {{name}} ל{{coords}}",
        edit_area: "{{extra}} ערוך אזור {{areaName}}",
        delete_area: "מחק אזור {{areaName}}",
        edit_note: "{{extra}} ערוך הערה {{noteTitle}}",
        delete_note: "מחק הערה {{noteTitle}}",
        edit_table: "{{extra}} ערוך טבלה {{tableName}}",
        delete_table: "מחק טבלה {{tableName}}",
        edit_type: "{{extra}} ערוך סוג {{typeName}}",
        delete_type: "מחק סוג {{typeName}}",
        add_relationship: "הוסף קשר",
        edit_relationship: "{{extra}} ערוך קשר {{refName}}",
        delete_relationship: "מחק קשר {{refName}}",
        not_found: "לא נמצא",
        pick_db: "בחר מסד נתונים",
        generic: "כללי",
        generic_description:
            "דיאגרמות כלליות יכולות להיות מיוצאות לכל SQL אך תומכות בסוגי נתונים מועטים.",
        enums: "מנויים",
        add_enum: "הוסף מנוי",
        edit_enum: "{{extra}} ערוך מנוי {{enumName}}",
        delete_enum: "מחק מנוי",
        enum_w_no_name: "נמצא מנוי ללא שם",
        enum_w_no_values: "נמצא מנוי '{{enumName}}' ללא ערכים",
        duplicate_enums: "מנויים כפולים בשם '{{enumName}}'",
        no_enums: "אין מנויים",
        no_enums_text: "הגדר מנויים כאן",
        declare_array: "הכרז מערך",
        empty_index_name: "הוכרז אינדקס ללא שם בטבלה '{{tableName}}'",
        didnt_find_diagram: "אופס! לא נמצאה הדיאגרמה.",
    },
};

export {he, hebrew};
