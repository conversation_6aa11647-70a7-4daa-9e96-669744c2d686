const czech = {
    name: "Czech",
    native_name: "<PERSON><PERSON><PERSON>",
    code: "cz",
};

const cz = {
    translation: {
        report_bug: "Nahlásit chybu",
        import: "Importovat",
        import_from: "Importovat z",
        file: "Soubor",
        new: "<PERSON><PERSON>",
        new_window: "Nové okno",
        open: "<PERSON>te<PERSON><PERSON><PERSON><PERSON>",
        save: "<PERSON><PERSON><PERSON><PERSON>",
        save_as: "<PERSON>lož<PERSON> jako",
        save_as_template: "Uložit jako šablonu",
        template_saved: "Šablona uložena!",
        rename: "<PERSON><PERSON><PERSON>menovat",
        delete_diagram: "Smazat diagram",
        are_you_sure_delete_diagram:
            "Opravdu chcete smazat tento diagram? Tato operace je nevratná.",
        oops_smth_went_wrong: "Jejda! Něco se pokazilo.",
        import_diagram: "Importní diagram",
        import_from_source: "Import z SQL",
        export_as: "Exportovat jako",
        export_source: "zdroj exportu",
        models: "<PERSON><PERSON>",
        exit: "<PERSON>ýst<PERSON>",
        edit: "<PERSON>ravit",
        undo: "<PERSON><PERSON><PERSON><PERSON><PERSON> zpět",
        redo: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        clear: "<PERSON><PERSON><PERSON>",
        are_you_sure_clear:
            "Opravdu chcete vymazat diagram? To je nevratné.",
        cut: "Střih",
        copy: "Kopie",
        paste: "Vložit",
        duplicate: "Duplikát",
        delete: "Odstranit",
        copy_as_image: "Kopírovat jako obrázek",
        view: "Pohled",
        header: "Nabídkový panel",
        sidebar: "Postranní panel",
        issues: "Problémy",
        presentation_mode: "Prezentační režim",
        strict_mode: "Přísný režim",
        field_details: "Podrobnosti pole",
        reset_view: "Obnovit zobrazení",
        show_grid: "Zobrazit mřížku",
        show_datatype: "Zobrazit datový typ",
        show_cardinality: "Ukažte kardinalitu",
        theme: "Téma",
        light: "Světlo",
        dark: "Tmavý",
        zoom_in: "Přiblížit",
        zoom_out: "Oddálit",
        fullscreen: "Celá obrazovka",
        settings: "Nastavení",
        show_timeline: "Zobrazit časovou osu",
        autosave: "Automatické ukládání",
        panning: "Panašování",
        show_debug_coordinates: "Zobrazit ladicí souřadnice",
        transform: "Transformovat",
        viewbox: "Zobrazit box",
        cursor_coordinates: "Souřadnice kurzoru",
        coordinate_space: "Prostor",
        coordinate_space_screen: "Obrazovka",
        coordinate_space_diagram: "Diagram",
        table_width: "Šířka stolu",
        language: "Jazyk",
        flush_storage: "Splachovací úložiště",
        are_you_sure_flush_storage:
            "Jste si jisti, že chcete úložiště propláchnout? Tím se nenávratně odstraní všechny vaše diagramy a vlastní šablony.",
        storage_flushed: "Úložiště propláchnuto",
        help: "Pomoc",
        shortcuts: "Zkratky",
        ask_on_discord: "Zeptejte se nás na Discord",
        feedback: "Zpětná vazba",
        no_changes: "Žádné změny",
        loading: "Načítání...",
        last_saved: "Naposledy uloženo",
        saving: "Úspora...",
        failed_to_save: "Nepodařilo se uložit",
        fit_window_reset: "Fit okno / Resetovat",
        zoom: "Přiblížení",
        add_table: "Přidat tabulku",
        add_area: "Přidat oblast",
        add_note: "Přidat poznámku",
        add_type: "Přidat typ",
        to_do: "Úkoly k provedení",
        tables: "Tabulky",
        relationships: "Vztahy",
        subject_areas: "Oblasti předmětu",
        notes: "Poznámky",
        types: "Typy",
        search: "Vyhledávání...",
        no_tables: "Žádné stoly",
        no_tables_text: "Začněte vytvářet svůj diagram!",
        no_relationships: "Žádné vztahy",
        no_relationships_text: "Přetažením propojte pole a vytvořte vztahy!",
        no_subject_areas: "Žádné tematické oblasti",
        no_subject_areas_text: "Přidejte tematické oblasti do skupinových tabulek!",
        no_notes: "Žádné poznámky",
        no_notes_text: "Použijte poznámky k zaznamenání dalších informací",
        no_types: "Žádné typy",
        no_types_text: "Vytvořte si vlastní typy dat",
        no_issues: "Nebyly zjištěny žádné problémy.",
        strict_mode_is_on_no_issues:
            "Přísný režim je vypnutý, takže se nebudou zobrazovat žádné problémy.",
        name: "Jméno",
        type: "Typ",
        null: "Null",
        not_null: "Není nulový",
        nullable: "Nullable",
        primary: "Primární",
        unique: "Unikátní",
        autoincrement: "Autoinkrementace",
        default_value: "Výchozí",
        check: "Zkontrolujte výraz",
        this_will_appear_as_is: "*To se objeví ve vygenerovaném skriptu tak, jak je.",
        comment: "Komentář",
        add_field: "Přidat pole",
        values: "Hodnoty",
        size: "Velikost",
        precision: "Přesnost",
        set_precision: "Nastavit přesnost: 'velikost, číslice'",
        use_for_batch_input: "Použijte , pro dávkové zadání",
        indices: "Indexy",
        add_index: "Přidat index",
        select_fields: "Vybraná pole",
        title: "Titul",
        not_set: "Nenastaveno",
        foreign: "Zahraniční",
        cardinality: "Kardinalita",
        on_update: "Při aktualizaci",
        on_delete: "Při smazání",
        swap: "Výměna",
        one_to_one: "Jedna ku jedné",
        one_to_many: "Jeden k mnoha",
        many_to_one: "Mnoho k jednomu",
        content: "Obsah",
        types_info:
            "Tato funkce je určena pro objektově-relační DBMS, jako je PostgreSQL.\nPři použití pro MySQL nebo MariaDB bude vygenerován typ JSON s odpovídající kontrolou platnosti json.\nPokud je použito pro SQLite, bude přeloženo na BLOB.\nPokud je použito pro MSSQL, bude vygenerován alias typu prvního pole.",
        table_deleted: "Tabulka vymazána",
        area_deleted: "Oblast smazána",
        note_deleted: "Poznámka smazána",
        relationship_deleted: "Vztah smazán",
        type_deleted: "Typ smazán",
        cannot_connect: "Nelze se připojit, sloupce mají různé typy",
        copied_to_clipboard: "Zkopírováno do schránky",
        create_new_diagram: "Vytvořte nový diagram",
        cancel: "Zrušit",
        open_diagram: "Otevřete diagram",
        rename_diagram: "Přejmenovat diagram",
        export: "Vývozní",
        export_image: "Export obrázku",
        create: "Vytvořit",
        confirm: "Potvrdit",
        last_modified: "Naposledy upraveno",
        drag_and_drop_files: "Přetáhněte soubor sem nebo jej nahrajte kliknutím.",
        upload_sql_to_generate_diagrams:
            "Nahrajte soubor sql pro automatické generování tabulek a sloupců.",
        overwrite_existing_diagram: "Přepsat existující diagram",
        only_mysql_supported:
            "*V současné době je podporováno načítání pouze skriptů MySQL.",
        blank: "Prázdný",
        filename: "Název souboru",
        table_w_no_name: "Deklarována tabulka bez názvu",
        duplicate_table_by_name: "Duplicitní tabulka podle názvu '{{tableName}}'",
        empty_field_name: "Prázdné pole `name` v tabulce '{{tableName}}'",
        empty_field_type: "Prázdné pole `type` v tabulce '{{tableName}}'",
        no_values_for_field:
            "'{{fieldName}}' pole tabulky '{{tableName}}' je typu `{{type}}` ale nebyly zadány žádné hodnoty",
        default_doesnt_match_type:
            "Výchozí hodnota pro pole '{{fieldName}}' v tabulce '{{tableName}}' neodpovídá svému typu",
        not_null_is_null:
            "'{{fieldName}}' pole tabulky '{{tableName}}' NENÍ NULL, ale má výchozí hodnotu NULL",
        duplicate_fields:
            "Duplicitní pole tabulky podle názvu '{{fieldName}}' v tabulce '{{tableName}}'",
        duplicate_index:
            "Duplicitní index podle názvu '{{indexName}}' v tabulce '{{tableName}}'",
        empty_index: "Index v tabulce '{{tableName}}' neindexuje žádné sloupce",
        no_primary_key: "Tabulka '{{tableName}}' nemá primární klíč",
        type_with_no_name: "Deklarován typ bez názvu",
        duplicate_types: "Duplicitní typy podle názvu '{{typeName}}'",
        type_w_no_fields: "Deklarován prázdný typ '{{typeName}}' bez polí",
        empty_type_field_name: "Prázdné pole `name` v typu '{{typeName}}'",
        empty_type_field_type: "Prázdné pole `type` v typu '{{typeName}}'",
        no_values_for_type_field:
            "'{{fieldName}}' pole typu '{{typeName}}' je typu `{{type}}` ale nebyly zadány žádné hodnoty",
        duplicate_type_fields:
            "Duplikujte pole typu podle názvu '{{fieldName}}' v typu '{{typeName}}'",
        duplicate_reference: "Duplicitní odkaz podle názvu '{{refName}}'",
        circular_dependency: "Kruhová závislost zahrnující tabulku '{{refName}}'",
        timeline: "Časová osa",
        priority: "Priorita",
        none: "Žádný",
        low: "Nízký",
        medium: "Střední",
        high: "Vysoký",
        sort_by: "Seřadit podle",
        my_order: "Moje objednávka",
        completed: "Dokončeno",
        alphabetically: "Abecedně",
        add_task: "Přidat úkol",
        details: "Podrobnosti",
        no_tasks: "Zatím nemáte žádné úkoly.",
        no_activity: "Zatím nemáte žádnou aktivitu.",
        move_element: "Pohyb {{name}} na {{coords}}",
        edit_area: "{{extra}} Upravit oblast {{areaName}}",
        delete_area: "Smazat oblast {{areaName}}",
        edit_note: "{{extra}} Upravit poznámku {{noteTitle}}",
        delete_note: "Smazat poznámku {{noteTitle}}",
        edit_table: "{{extra}} Upravit tabulku {{tableName}}",
        delete_table: "Smazat tabulku {{tableName}}",
        edit_type: "{{extra}} Upravit typ {{typeName}}",
        delete_type: "Smazat typ {{typeName}}",
        add_relationship: "Přidat vztah",
        edit_relationship: "{{extra}} Edit the relationship {{refName}}",
        delete_relationship: "Smazat vztah {{refName}}",
        not_found: "Nenalezeno",
        pick_db: "Vyberte databázi",
        generic: "Obecný",
        generic_description:
            "Obecné diagramy lze exportovat do libovolného jazyka SQL, ale podporují jen několik datových typů.",
        enums: "enum",
        add_enum: "Přidat enum",
        edit_enum: "{{extra}} Upravit enum {{enumName}}",
        delete_enum: "Smazat enum",
        enum_w_no_name: "Nalezen enum bez jména",
        enum_w_no_values: "Nalezeno enum '{{enumName}}' bez hodnot",
        duplicate_enums: "Duplicitní enums se jménem '{{enumName}}'",
        no_enums: "Žádné enums",
        no_enums_text: "Zde definujte enums",
        declare_array: "Deklarace pole",
        empty_index_name: "Deklarován index bez názvu v tabulce '{{tableName}}'",
        didnt_find_diagram: "Ups! Nenašel jsem diagram.",
        unsigned: "Nepodepsaný",
        share: "Podíl",
        unshare: "Zrušit sdílení",
        copy_link: "Zkopírujte odkaz",
        readme: "README",
        failed_to_load: "Načtení se nezdařilo. Ujistěte se, že je odkaz správný.",
        share_info:
            "* Sdílením tohoto odkazu nevznikne živá relace spolupráce v reálném čase.",
        show_relationship_labels: "Zobrazit štítky vztahů",
        docs: "Docs",
        supported_types: "Podporované typy souborů:",
        bulk_update: "Hromadná aktualizace",
        multiselect: "Vícenásobný výběr",
        export_saved_data: "Export uložených dat",
    },
};

export {cz, czech};
