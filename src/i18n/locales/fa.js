const persian = {
    name: "Persian",
    native_name: "فار<PERSON>ی",
    code: "fa",
};

const fa = {
    translation: {
        report_bug: "گزارش خطا",
        import_from: "وارد کردن",
        import: "وارد کردن",
        file: "فایل",
        new: "جدید",
        new_window: "پنجره جدید",
        open: "باز کردن",
        save: "ذخیره",
        save_as: "ذخیره به عنوان",
        save_as_template: "ذخیره به عنوان الگو",
        template_saved: "الگو ذخیره شد!",
        rename: "تغییر نام",
        delete_diagram: "حذف نمودار",
        are_you_sure_delete_diagram:
            "آیا مطمئن هستید که می‌خواهید این نمودار را حذف کنید؟ این عملیات غیر قابل برگشت است.",
        oops_smth_went_wrong: "اوه! مشکلی پیش آمد.",
        import_diagram: "وارد کردن نمودار",
        import_from_source: "وارد کردن از SQL",
        export_as: "صادر کردن به عنوان",
        export_source: "صادر کردن SQL",
        models: "مدل‌ها",
        exit: "خروج",
        edit: "ویرایش",
        undo: "واگرد",
        redo: "از نو",
        clear: "پاک کردن",
        are_you_sure_clear:
            "آیا مطمئن هستید که می‌خواهید نمودار را پاک کنید؟ این عملیات غیر قابل برگشت است.",
        cut: "برش",
        copy: "کپی",
        paste: "چسباندن",
        duplicate: "تکرار",
        delete: "حذف",
        copy_as_image: "کپی به عنوان تصویر",
        view: "مشاهده",
        header: "نوار منو",
        sidebar: "نوار کناری",
        issues: "مشکلات",
        presentation_mode: "حالت ارائه",
        strict_mode: "حالت سختگیرانه",
        field_details: "جزئیات فیلد",
        reset_view: "بازنشانی نما",
        show_grid: "نمایش شبکه",
        show_cardinality: "نمایش کاردینالیته",
        theme: "تم",
        light: "روشن",
        dark: "تاریک",
        zoom_in: "بزرگنمایی",
        zoom_out: "کوچکنمایی",
        fullscreen: "تمام صفحه",
        settings: "تنظیمات",
        show_timeline: "نمایش خط زمانی",
        autosave: "ذخیره خودکار",
        panning: "پانینگ",
        table_width: "عرض جدول",
        language: "زبان",
        flush_storage: "پاک کردن حافظه",
        are_you_sure_flush_storage:
            "آیا مطمئن هستید که می‌خواهید حافظه را پاک کنید؟ این عمل تمام نمودارها و الگوهای سفارشی شما را به طور غیر قابل برگشت حذف می‌کند.",
        storage_flushed: "حافظه پاک شد",
        help: "راهنما",
        shortcuts: "میانبرها",
        ask_on_discord: "پرسیدن در Discord",
        feedback: "بازخورد",
        no_changes: "بدون تغییرات",
        loading: "در حال بارگذاری...",
        last_saved: "آخرین ذخیره",
        saving: "در حال ذخیره...",
        failed_to_save: "ذخیره‌سازی ناموفق",
        fit_window_reset: "تنظیم پنجره / بازنشانی",
        zoom: "بزرگنمایی",
        add_table: "افزودن جدول",
        add_area: "افزودن ناحیه",
        add_note: "افزودن یادداشت",
        add_type: "افزودن نوع",
        to_do: "برای انجام",
        tables: "جدول‌ها",
        relationships: "روابط",
        subject_areas: "ناحیه‌های موضوعی",
        notes: "یادداشت‌ها",
        types: "نوع‌ها",
        search: "جستجو...",
        no_tables: "بدون جدول",
        no_tables_text: "شروع به ساخت نمودار خود کنید!",
        no_relationships: "بدون رابطه",
        no_relationships_text: "برای اتصال فیلدها و تشکیل روابط بکشید!",
        no_subject_areas: "بدون ناحیه موضوعی",
        no_subject_areas_text:
            "ناحیه‌های موضوعی را برای گروه‌بندی جدول‌ها اضافه کنید!",
        no_notes: "بدون یادداشت",
        no_notes_text: "از یادداشت‌ها برای ثبت اطلاعات اضافی استفاده کنید",
        no_types: "بدون نوع",
        no_types_text: "نوع داده سفارشی خود را بسازید",
        no_issues: "هیچ مشکلی شناسایی نشد.",
        strict_mode_is_on_no_issues:
            "حالت سختگیرانه خاموش است بنابراین هیچ مشکلی نمایش داده نخواهد شد.",
        name: "نام",
        type: "نوع",
        null: "خالی",
        not_null: "غیر خالی",
        primary: "اصلی",
        unique: "یگانه",
        autoincrement: "افزایش خودکار",
        default_value: "پیش‌فرض",
        check: "عبارت بررسی",
        this_will_appear_as_is:
            "*این در اسکریپت تولید شده به همان صورت نمایش داده می‌شود.",
        comment: "نظر",
        add_field: "افزودن فیلد",
        values: "مقادیر",
        size: "اندازه",
        precision: "دقت",
        set_precision: "تنظیم دقت: (اندازه، ارقام)",
        use_for_batch_input: "برای ورودی دسته‌ای از , استفاده کنید",
        indices: "شاخص‌ها",
        add_index: "افزودن شاخص",
        select_fields: "انتخاب فیلدها",
        title: "عنوان",
        not_set: "تنظیم نشده",
        foreign: "خارجی",
        cardinality: "کاردینالیته",
        on_update: "در به‌روزرسانی",
        on_delete: "در حذف",
        swap: "جابجایی",
        one_to_one: "یک به یک",
        one_to_many: "یک به چند",
        many_to_one: "چند به یک",
        content: "محتوا",
        types_info:
            "این ویژگی برای DBMS‌های شیء‌گرا مانند PostgreSQL طراحی شده است.\nاگر برای MySQL یا MariaDB استفاده شود، نوع JSON با بررسی اعتبار json متناظر تولید می‌شود.\nاگر برای SQLite استفاده شود، به BLOB ترجمه می‌شود.\nاگر برای MSSQL استفاده شود، یک نام مستعار نوع برای فیلد اول تولید می‌شود.",
        table_deleted: "جدول حذف شد",
        area_deleted: "ناحیه حذف شد",
        note_deleted: "یادداشت حذف شد",
        relationship_deleted: "رابطه حذف شد",
        type_deleted: "نوع حذف شد",
        cannot_connect: "اتصال غیر ممکن است، ستون‌ها انواع مختلفی دارند",
        copied_to_clipboard: "به کلیپ‌بورد کپی شد",
        create_new_diagram: "ایجاد نمودار جدید",
        cancel: "لغو",
        open_diagram: "باز کردن نمودار",
        rename_diagram: "تغییر نام نمودار",
        export: "صادر کردن",
        export_image: "صادر کردن تصویر",
        create: "ایجاد",
        confirm: "تأیید",
        last_modified: "آخرین ویرایش",
        drag_and_drop_files:
            "فایل را اینجا بکشید و رها کنید یا کلیک کنید تا بارگذاری شود.",
        upload_sql_to_generate_diagrams:
            "یک فایل sql را بارگذاری کنید تا جداول و ستون‌هایتان به‌طور خودکار تولید شوند.",
        overwrite_existing_diagram: "بازنویسی نمودار موجود",
        only_mysql_supported:
            "*در حال حاضر فقط بارگذاری اسکریپت‌های MySQL پشتیبانی می‌شود.",
        blank: "خالی",
        filename: "نام فایل",
        table_w_no_name: "جدولی بدون نام اعلام شد",
        duplicate_table_by_name: "جدول تکراری با نام '{{tableName}}'",
        empty_field_name: "فیلد خالی name در جدول '{{tableName}}'",
        empty_field_type: "فیلد خالی type در جدول '{{tableName}}'",
        no_values_for_field:
            "فیلد '{{fieldName}}' جدول '{{tableName}}' از نوع {{type}} است اما هیچ مقداری مشخص نشده است",
        default_doesnt_match_type:
            "مقدار پیش‌فرض برای فیلد '{{fieldName}}' در جدول '{{tableName}}' با نوع آن مطابقت ندارد",
        not_null_is_null:
            "فیلد '{{fieldName}}' جدول '{{tableName}}' غیر خالی است اما پیش‌فرض آن خالی است",
        duplicate_fields:
            "فیلدهای تکراری جدول به نام '{{fieldName}}' در جدول '{{tableName}}'",
        duplicate_index:
            "شاخص تکراری به نام '{{indexName}}' در جدول '{{tableName}}'",
        empty_index: "شاخص در جدول '{{tableName}}' هیچ ستونی را شاخص نمی‌کند",
        no_primary_key: "جدول '{{tableName}}' کلید اصلی ندارد",
        type_with_no_name: "نوعی بدون نام اعلام شد",
        duplicate_types: "انواع تکراری به نام '{{typeName}}'",
        type_w_no_fields: "نوع خالی '{{typeName}}' بدون فیلد اعلام شد",
        empty_type_field_name: "فیلد `name` خالی در نوع '{{typeName}}'",
        empty_type_field_type: "فیلد `type` خالی در نوع '{{typeName}}'",
        no_values_for_type_field:
            "'{{fieldName}}' فیلد از نوع '{{typeName}}' از نوع `{{type}}` است اما هیچ مقداری مشخص نشده است",
        duplicate_type_fields:
            "فیلدهای تکراری نوع با نام '{{fieldName}}' در نوع '{{typeName}}'",
        duplicate_reference: "ارجاع تکراری به نام '{{refName}}'",
        circular_dependency: "وابستگی دورانی شامل جدول '{{refName}}'",
        timeline: "جدول زمانی",
        priority: "اولویت",
        none: "هیچکدام",
        low: "کم",
        medium: "متوسط",
        high: "زیاد",
        sort_by: "مرتب‌سازی بر اساس",
        my_order: "ترتیب من",
        completed: "تکمیل شده",
        alphabetically: "الفبایی",
        add_task: "اضافه کردن کار",
        details: "جزئیات",
        no_tasks: "هنوز کاری ندارید.",
        no_activity: "هنوز فعالیتی ندارید.",
        move_element: "انتقال {{name}} به {{coords}}",
        edit_area: "{{extra}} ویرایش ناحیه {{areaName}}",
        delete_area: "حذف ناحیه {{areaName}}",
        edit_note: "{{extra}} ویرایش یادداشت {{noteTitle}}",
        delete_note: "حذف یادداشت {{noteTitle}}",
        edit_table: "{{extra}} ویرایش جدول {{tableName}}",
        delete_table: "حذف جدول {{tableName}}",
        edit_type: "{{extra}} ویرایش نوع {{typeName}}",
        delete_type: "حذف نوع {{typeName}}",
        add_relationship: "اضافه کردن ارتباط",
        edit_relationship: "{{extra}} ویرایش ارتباط {{refName}}",
        delete_relationship: "حذف ارتباط {{refName}}",
        not_found: "یافت نشد",
    },
};

export {fa, persian};
