const russian = {
    name: "Russian",
    native_name: "Русский",
    code: "ru",
};

const ru = {
    translation: {
        report_bug: "Сообщить об ошибке",
        import_from: "Импорт",
        import: "Импорт",
        file: "Файл",
        new: "Новый",
        new_window: "Новое окно",
        open: "Открыть",
        save: "Сохранить",
        save_as: "Сохранить как",
        save_as_template: "Сохранить как шаблон",
        template_saved: "Шаблон сохранен!",
        rename: "Переименовать",
        delete_diagram: "Удалить диаграмму",
        are_you_sure_delete_diagram:
            "Вы уверены, что хотите удалить эту диаграмму? Эта операция необратима.",
        oops_smth_went_wrong: "Упс! Что-то пошло не так.",
        import_diagram: "Импорт диаграммы",
        import_from_source: "Импорт из SQL",
        export_as: "Экспорт как",
        export_source: "Экспорт в SQL",
        models: "Модели",
        exit: "Выход",
        edit: "Редактировать",
        undo: "Отменить",
        redo: "Повторить",
        clear: "Очистить",
        are_you_sure_clear:
            "Вы уверены, что хотите очистить диаграмму? Это необратимо.",
        cut: "Вырезать",
        copy: "Копировать",
        paste: "Вставить",
        duplicate: "Дублировать",
        delete: "Удалить",
        copy_as_image: "Копировать как изображение",
        view: "Вид",
        header: "Меню",
        sidebar: "Боковая панель",
        issues: "Проблемы",
        presentation_mode: "Режим презентации",
        strict_mode: "Строгий режим",
        field_details: "Подробности поля",
        reset_view: "Сбросить вид",
        show_grid: "Показать сетку",
        show_cardinality: "Показать кардинальность",
        theme: "Тема",
        light: "Светлая",
        dark: "Темная",
        zoom_in: "Увеличить",
        zoom_out: "Уменьшить",
        fullscreen: "Полноэкранный режим",
        settings: "Настройки",
        show_timeline: "Показать временную шкалу",
        autosave: "Автосохранение",
        panning: "Панорамирование",
        table_width: "Ширина таблицы",
        language: "Язык",
        flush_storage: "Очистить хранилище",
        are_you_sure_flush_storage:
            "Вы уверены, что хотите очистить хранилище? Это необратимо удалит все ваши диаграммы и пользовательские шаблоны.",
        storage_flushed: "Хранилище очищено",
        help: "Помощь",
        shortcuts: "Горячие клавиши",
        ask_on_discord: "Задайте вопрос в Discord",
        feedback: "Обратная связь",
        no_changes: "Нет изменений",
        loading: "Загрузка...",
        last_saved: "Последнее сохранение",
        saving: "Сохранение...",
        failed_to_save: "Не удалось сохранить",
        fit_window_reset: "Подогнать к окну / Сбросить",
        zoom: "Масштаб",
        add_table: "Добавить таблицу",
        add_area: "Добавить область",
        add_note: "Добавить заметку",
        add_type: "Добавить тип",
        to_do: "Делать",
        tables: "Таблицы",
        relationships: "Отношения",
        subject_areas: "Области предметной области",
        notes: "Заметки",
        types: "Типы",
        search: "Поиск...",
        no_tables: "Нет таблиц",
        no_tables_text: "Начните создавать свою диаграмму!",
        no_relationships: "Нет отношений",
        no_relationships_text:
            "Перетащите, чтобы соединить поля и формировать отношения!",
        no_subject_areas: "Нет предметных областей",
        no_subject_areas_text:
            "Добавьте предметные области для группировки таблиц!",
        no_notes: "Нет заметок",
        no_notes_text: "Используйте заметки для записи дополнительной информации",
        no_types: "Нет типов",
        no_types_text: "Создайте собственные пользовательские типы данных",
        no_issues: "Проблемы не обнаружены.",
        strict_mode_is_on_no_issues:
            "Строгий режим выключен, поэтому проблемы не будут отображаться.",
        name: "Имя",
        type: "Тип",
        null: "Null",
        not_null: "Не Null",
        primary: "Первичный",
        unique: "Уникальный",
        autoincrement: "Автоувеличение",
        default_value: "Значение по умолчанию",
        check: "Проверка выражения",
        this_will_appear_as_is:
            "*Это будет отображаться в сгенерированном скрипте как есть.",
        comment: "Комментарий",
        add_field: "Добавить поле",
        values: "значения",
        size: "Размер",
        precision: "Точность",
        set_precision: "Установить точность: (размер, цифры)",
        use_for_batch_input: "Использовать , для пакетного ввода",
        indices: "Индексы",
        add_index: "Добавить индекс",
        select_fields: "Выберите поля",
        title: "Заголовок",
        not_set: "Не установлено",
        foreign: "Внешний",
        cardinality: "Кардинальность",
        on_update: "При обновлении",
        on_delete: "При удалении",
        swap: "Поменять",
        one_to_one: "Один к одному",
        one_to_many: "Один ко многим",
        many_to_one: "Многие к одному",
        content: "Содержание",
        types_info:
            "Эта функция предназначена для объектно-реляционных СУБД, таких как PostgreSQL.\nПри использовании для MySQL или MariaDB будет сгенерирован тип JSON с соответствующей проверкой json.\nПри использовании для SQLite он будет преобразован в BLOB.\nПри использовании для MSSQL будет сгенерирован псевдоним типа для первого поля.",
        table_deleted: "Таблица удалена",
        area_deleted: "Область удалена",
        note_deleted: "Заметка удалена",
        relationship_deleted: "Отношение удалено",
        type_deleted: "Тип удален",
        cannot_connect: "Невозможно подключиться, колонки имеют разные типы",
        copied_to_clipboard: "Скопировано в буфер обмена",
        create_new_diagram: "Создать новую диаграмму",
        cancel: "Отмена",
        open_diagram: "Открыть диаграмму",
        rename_diagram: "Переименовать диаграмму",
        export: "Экспорт",
        export_image: "Экспорт изображения",
        create: "Создать",
        confirm: "Подтвердить",
        last_modified: "Последнее изменение",
        drag_and_drop_files: "Перетащите файл сюда или нажмите, чтобы загрузить.",
        upload_sql_to_generate_diagrams:
            "Загрузите файл sql для автогенерации ваших таблиц и колонок.",
        overwrite_existing_diagram: "Перезаписать существующую диаграмму",
        only_mysql_supported:
            "*На данный момент поддерживается только загрузка скриптов MySQL.",
        blank: "Пустой",
        filename: "Имя файла",
        table_w_no_name: "Объявлена таблица без названия",
        duplicate_table_by_name: "Дублировать таблицу по имени '{{tableName}}'",
        empty_field_name: "Пустое поле `name` в таблице '{{tableName}}'",
        empty_field_type: "Пустое поле `type` в таблице '{{tableName}}'",
        no_values_for_field:
            "Поле '{{fieldName}}' таблицы '{{tableName}}' имеет тип `{{type}}`, но не указаны значения",
        default_doesnt_match_type:
            "Значение по умолчанию для поля '{{fieldName}}' в таблице '{{tableName}}' не соответствует его типу",
        not_null_is_null:
            "Поле '{{fieldName}}' таблицы '{{tableName}}' НЕ NULL, но имеет значение NULL по умолчанию",
        duplicate_fields:
            "Дублирование полей таблицы по имени '{{fieldName}}' в таблице '{{tableName}}'",
        duplicate_index:
            "Дублирование индекса по имени '{{indexName}}' в таблице '{{tableName}}'",
        empty_index: "Индекс в таблице '{{tableName}}' не индексирует столбцы",
        no_primary_key: "В таблице '{{tableName}}' нет первичного ключа",
        type_with_no_name: "Объявлен тип без названия",
        duplicate_types: "Дублирование типов по имени '{{typeName}}'",
        type_w_no_fields: "Объявлен пустой тип '{{typeName}}' без полей",
        empty_type_field_name: "Пустое поле `name` в типе '{{typeName}}'",
        empty_type_field_type: "Пустое поле `type` в типе '{{typeName}}'",
        no_values_for_type_field:
            "Поле '{{fieldName}}' типа '{{typeName}}' имеет тип `{{type}}`, но не указаны значения",
        duplicate_type_fields:
            "Дублирование полей типа по имени '{{fieldName}}' в типе '{{typeName}}'",
        duplicate_reference: "Дублирование ссылки по имени '{{refName}}'",
        circular_dependency:
            "Циклическая зависимость, вовлекающая таблицу '{{refName}}'",
        timeline: "Временная шкала",
        priority: "Приоритет",
        none: "Нет",
        low: "Низкий",
        medium: "Средний",
        high: "Высокий",
        sort_by: "Сортировать по",
        my_order: "Мой порядок",
        completed: "Завершено",
        alphabetically: "По алфавиту",
        add_task: "Добавить задачу",
        details: "Подробности",
        no_tasks: "У вас еще нет задач.",
        no_activity: "У вас пока нет активности.",
        move_element: "Переместить {{name}} в {{coords}}",
        edit_area: "{{extra}} Редактировать область {{areaName}}",
        delete_area: "Удалить область {{areaName}}",
        edit_note: "{{extra}} Редактировать заметку {{noteTitle}}",
        delete_note: "Удалить заметку {{noteTitle}}",
        edit_table: "{{extra}} Редактировать таблицу {{tableName}}",
        delete_table: "Удалить таблицу {{tableName}}",
        edit_type: "{{extra}} Редактировать тип {{typeName}}",
        delete_type: "Удалить тип {{typeName}}",
        add_relationship: "Добавить отношение",
        edit_relationship: "{{extra}} Редактировать отношение {{refName}}",
        delete_relationship: "Удалить отношение {{refName}}",
        not_found: "Не найдено",
        pick_db: "Выберите базу данных",
        generic: "Общий",
        generic_description:
            "Общие диаграммы можно экспортировать в любой вариант SQL, но они поддерживают несколько типов данных.",
        enums: "Перечисления",
        add_enum: "Добавить перечисление",
        edit_enum: "{{extra}} Изменить перечисление {{enumName}}",
        delete_enum: "Удалить перечисление",
        enum_w_no_name: "Найдено перечисление без имени",
        enum_w_no_values:
            "Найдено перечисление '{{enumName}}' без каких-либо значений",
        duplicate_enums: "Повторяющиеся перечисления с именем '{{enumName}}'",
        no_enums: "Никаких перечислений",
        no_enums_text: "Определите перечисления здесь",
        declare_array: "Объявите массив",
        empty_index_name: "Объявленный индекс без имени в таблице '{{tableName}}'",
        didnt_find_diagram: "Упс! Не нашел диаграмму.",
        unsigned: "Неподписанный",
        share: "Поделиться",
        unshare: "Не делиться",
        copy_link: "Скопировать ссылку",
        readme: "README",
        failed_to_load:
            "Не удалось загрузить. Убедитесь, что ссылка указана правильно.",
        share_info:
            "* Поделившись этой ссылкой, не будет создано сеанса совместной работы в режиме реального времени.",
    },
};

export {ru, russian};
