const turkish = {
    name: "Turkish",
    native_name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    code: "tr",
};

const tr = {
    translation: {
        report_bug: "Hata bildir",
        import_from: "İçe aktar",
        import: "<PERSON>çe aktar",
        file: "<PERSON><PERSON><PERSON>",
        new: "<PERSON>ni",
        new_window: "Yeni pencere",
        open: "A<PERSON>",
        save: "<PERSON><PERSON>",
        save_as: "Farklı kaydet",
        save_as_template: "Şablon olarak kaydet",
        template_saved: "Şablon kaydedildi",
        rename: "Yeniden adlandır",
        delete_diagram: "Diagramı sil",
        are_you_sure_delete_diagram:
            "Diagramı silmek istediğinizden emin misiniz? Bu işlem geri döndürülemez.",
        oops_smth_went_wrong: "Oops! Bir şeyler yanlış gitti.",
        import_diagram: "Diagramı içe aktar",
        import_from_source: "SQL dosyasından içe aktar",
        export_as: "<PERSON>ışa aktar",
        export_source: "SQL dosyası olarak dışa aktar",
        models: "Modeller",
        exit: "<PERSON><PERSON>kı<PERSON>",
        edit: "<PERSON><PERSON><PERSON><PERSON>",
        undo: "<PERSON><PERSON> al",
        redo: "İleri al",
        clear: "Temizle",
        are_you_sure_clear:
            "Diagramı temizlemek istediğinizden emin misiniz? Bu işlem geri döndürülemez",
        cut: "Kes",
        copy: "Kopyala",
        paste: "Yapıştır",
        duplicate: "Çoğalt",
        delete: "Sil",
        copy_as_image: "Resim olarak kopyala",
        view: "Görünüm",
        header: "Menü çubuğu",
        sidebar: "Kenar çubuğu",
        issues: "Sorunlar",
        presentation_mode: "Sunum modu",
        strict_mode: "Sabit mod",
        field_details: "Alan detayları",
        reset_view: "Görünümü sıfırla",
        show_grid: "Izgarayı göster",
        show_cardinality: "Nicelikleri göster",
        theme: "Tema",
        light: "Açık",
        dark: "Koyu",
        zoom_in: "Yakınlaştır",
        zoom_out: "Uzaklaştır",
        fullscreen: "Tam ekran",
        settings: "Ayarlar",
        show_timeline: "Zaman çizelgesini göster",
        autosave: "Otomatik kaydet",
        panning: "Yatay kaydırma",
        show_debug_coordinates: "Hata ayıklama koordinatlarını göster",
        transform: "Dönüştür",
        viewbox: "Görünüm kutusu",
        cursor_coordinates: "İmleç koordinatları",
        coordinate_space: "Koordinat uzayı",
        coordinate_space_screen: "Ekran",
        coordinate_space_diagram: "Diagram",
        table_width: "Tablo genişliği",
        language: "Dil",
        flush_storage: "Depoyu temizle",
        are_you_sure_flush_storage:
            "Depoyu temizlemek istediğinizden emin misiniz? Bu işlem geri döndürülemez.",
        storage_flushed: "Depo temizlendi",
        help: "Yardım",
        shortcuts: "Kısayollar",
        ask_on_discord: "Discord'da sor",
        feedback: "Geribildirim",
        no_changes: "Değişiklik yok",
        loading: "Yükleniyor...",
        last_saved: "Son kaydedilen",
        saving: "Kaydediliyor...",
        failed_to_save: "Kaydetme başarısız oldu",
        fit_window_reset: "Pencere sığdırma & Sıfırla",
        zoom: "Yakınlaştırma",
        add_table: "Tablo ekle",
        add_area: "Alan ekle",
        add_note: "Not ekle",
        add_type: "Tip ekle",
        to_do: "Yapılacaklar",
        tables: "Tablolar",
        relationships: "İlişkiler",
        subject_areas: "Konu alanları",
        notes: "Notlar",
        types: "Tipler",
        search: "Ara...",
        no_tables: "Tablo yok",
        no_tables_text: "Diagram oluşturmaya başlayın",
        no_relationships: "İlişki yok",
        no_relationships_text:
            "Alanları bağlamak ve ilişkiler oluşturmak için sürükleyin",
        no_subject_areas: "Konu alanı yok",
        no_subject_areas_text: "Grup tablolarına konu alanları ekleyin!",
        no_notes: "Not yok",
        no_notes_text: "Ekstra bilgi kaydetmek için notları kullanın",
        no_types: "Tip yok",
        no_types_text: "Kendi özel veri türlerinizi tanımlayın",
        no_issues: "Hiçbir sorun bulunamadı",
        strict_mode_is_on_no_issues: "Sabit mod açık iken hatalar görüntülenmez",
        name: "",
        type: "Tip",
        null: "Boş",
        not_null: "Boş değil",
        primary: "Birincil",
        unique: "Benzersiz",
        autoincrement: "Otomatik artan",
        default_value: "Varsayılan değer",
        check: "Kontrol",
        this_will_appear_as_is: "*Bu, oluşturulan kodda olduğu gibi görünecektir.",
        comment: "Yorum",
        add_field: "Alan ekle",
        values: "Değerler",
        size: "Boyut",
        precision: "Hassasiyet",
        set_precision: "Hassasiyeti Ayarlar: (boyut, rakamlar)",
        use_for_batch_input: "Toplu girdi için , kullanın",
        indices: "İndeksler",
        add_index: "İndeks ekle",
        select_fields: "Alanları seç",
        title: "Başlık",
        not_set: "Ayarlanmadı",
        foreign: "Yabancı",
        cardinality: "Kardinalite",
        on_update: "Güncelleme yap",
        on_delete: "Silme yap",
        swap: "Değiştir",
        one_to_one: "Bire bir",
        one_to_many: "Bire çok",
        many_to_one: "Çoktan bire",
        content: "İçerik",
        types_info:
            "Bu özellik PostgreSQL gibi nesne-ilişkisel DBMS'ler içindir.\nMySQL veya MariaDB için kullanılırsa, ilgili json doğrulama kontrolüyle birlikte bir JSON türü oluşturulacaktır.\nSQLite için kullanılırsa, bir BLOB'a çevrilecektir.\nMSQL için kullanılırsa, ilk alana bir tür takma adı oluşturulacaktır.",
        table_deleted: "Tablo silindi",
        area_deleted: "Alan silindi",
        note_deleted: "Not silindi",
        relationship_deleted: "İlişki silindi",
        type_deleted: "Tip silindi",
        cannot_connect: "Bağlanamıyor, sütunların türleri farklı",
        copied_to_clipboard: "Panoya kopyalandı",
        create_new_diagram: "Yeni diagram oluştur",
        cancel: "İptal",
        open_diagram: "Diagramı aç",
        rename_diagram: "Diagramı yeniden adlandır",
        export: "Dışa aktar",
        export_image: "Resim olarak dışa aktar",
        create: "Oluştur",
        confirm: "Onayla",
        last_modified: "Son değişiklik",
        drag_and_drop_files:
            "Dosyayı buraya sürükleyip bırakın veya yüklemek için tıklayın.",
        upload_sql_to_generate_diagrams:
            "Tablolarınızı ve sütunlarınızı otomatik olarak oluşturmak için bir sql dosyası yükleyin.",
        overwrite_existing_diagram: "Mevcut diyagramı üzerine yaz",
        only_mysql_supported:
            "*Şimdilik sadece MySQL veritabanları desteklenmektedir.",
        blank: "Boş",
        filename: "Dosya adı",
        table_w_no_name: "Adı olmayan tablo belirlendi",
        duplicate_table_by_name: "Tekrar eden tablo adı '{{tableName}}'",
        empty_field_name: "'{{tableName}} tablosunda boş alan `name`",
        empty_field_type: "'{{tableName}} tablosunda boş tip `type`",
        no_values_for_field:
            "'{{tableName}}' tablosunun '{{fieldName}}' alanı `{{type}}` türündedir ancak hiçbir değer belirtilmemiştir",
        default_doesnt_match_type:
            "{{tableName}} tablosundaki '{{fieldName}}' alanı için varsayılan değer türüyle eşleşmiyor",
        not_null_is_null:
            "'{{tableName}}' tablosunun '{{fieldName}}' alanı NULL DEĞİL ancak varsayılan olarak NULL",
        duplicate_fields:
            "Tablo '{{tableName}}' içinde '{{fieldName}}' adıyla yinelenen tablo alanları",
        duplicate_index:
            "'{{tableName}}' tablosunda '{{indexName}}' adına göre yinelenen dizin",
        empty_index:
            "'{{tableName}}' tablosundaki dizin hiçbir sütunu indexlemiyor",
        no_primary_key: "'{{tableName}} tablosunun birincil anahtarı yok",
        type_with_no_name: "Adı olmayan bir tür bildirilmiş",
        duplicate_types: "'{{typeName}}' adına göre yinelenen türler",
        type_w_no_fields: "Alanı olmayan boş bir '{{typeName}}' türü bildirilmiş",
        empty_type_field_name: "Boş alan `name` in type '{{typeName}}'",
        empty_type_field_type: "Boş alan `type` '{{typeName}}' türünde",
        no_values_for_type_field:
            "'{{typeName}}' türündeki '{{fieldName}}' alanı `{{type}}` türündedir ancak hiçbir değer belirtilmemiştir",
        duplicate_type_fields:
            "'{{typeName}}' türünde '{{fieldName}}' adına göre yinelenen tür alanları",
        duplicate_reference: "'{{refName}}' adıyla yinelenen referans",
        circular_dependency: "Tablo '{{refName}}' içeren döngüsel bağımlılık",
        timeline: "Zaman çizelgesi",
        priority: "Öncelik",
        none: "Hiçbiri",
        low: "Düşük",
        medium: "Orta",
        high: "Yüksek",
        sort_by: "Sırala",
        my_order: "Benim sıram",
        completed: "Tamamlandı",
        alphabetically: "Alfabetik",
        add_task: "Görev ekle",
        details: "Detaylar",
        no_tasks: "Henüz bir görev yok",
        no_activity: "Henüz bir etkinlik yok",
        move_element: "{{name}} öğesini {{coords}} konumuna taşı",
        edit_area: "{{extra}} Alanı düzenle {{areaName}}",
        delete_area: "Alanı sil {{areaName}}",
        edit_note: "{{extra}} Notu düzenle {{noteTitle}}",
        delete_note: "Notu sil {{noteTitle}}",
        edit_table: "{{extra}} Tabloyu düzenle {{tableName}}",
        delete_table: "Tabloyu sil {{tableName}}",
        edit_type: "{{extra}} Türü düzenle {{typeName}}",
        delete_type: "Tip sil {{typeName}}",
        add_relationship: "İlişki ekle",
        edit_relationship: "{{extra}} İlişkiyi düzenle {{refName}}",
        delete_relationship: "İlişkiyi sil {{refName}}",
        not_found: "Bulunamadı",
        pick_db: "Veritabanı seç",
        generic: "Genel",
        generic_description:
            "Genel diyagramlar herhangi bir SQL türüne dışa aktarılabilir ancak az sayıda veri türünü destekler.",
        enums: "Enums",
        add_enum: "Enum ekle",
        edit_enum: "{{extra}} Enum'u düzenle {{enumName}}",
        delete_enum: "Enum'u sil",
        enum_w_no_name: "İsimsiz bir enum bulundu",
        enum_w_no_values: "Değerleri olmayan '{{enumName}}' adlı enum bulundu",
        duplicate_enums: "'{{enumName}}' adında yinelenen enumlar var",
        no_enums: "Enum yok",
        no_enums_text: "Burada enum tanımlayın",
        declare_array: "Dizi bildir",
        empty_index_name: "'{{tableName}}' tablosunda isimsiz bir dizin bildirildi",
        didnt_find_diagram: "Oops! Diyagram bulunamadı.",
    },
};

export {tr, turkish};
