const ukrainian = {
    name: "Ukrainian",
    native_name: "Українська",
    code: "uk",
};

const uk = {
    translation: {
        report_bug: "Повідомити про помилку",
        import_from: "Імпортувати",
        import: "Імпортувати",
        file: "Файл",
        new: "Новий",
        new_window: "Нове вікно",
        open: "Відкрити",
        save: "Зберегти",
        save_as: "Зберегти як",
        save_as_template: "Зберегти як шаблон",
        template_saved: "Шаблон збережено!",
        rename: "Перейменувати",
        delete_diagram: "Видалити діаграму",
        are_you_sure_delete_diagram:
            "Ви впевнені, що хочете видалити цю діаграму? Ця операція є незворотною.",
        oops_smth_went_wrong: "Упс! Щось пішло не так.",
        import_diagram: "Імпортувати діаграму",
        import_from_source: "Імпортувати з SQL",
        export_as: "Експортувати як",
        export_source: "Експортувати SQL",
        models: "Моделі",
        exit: "Вихід",
        edit: "Редагувати",
        undo: "Скасувати",
        redo: "Повторити",
        clear: "Очистити",
        are_you_sure_clear:
            "Ви впевнені, що хочете очистити діаграму? Це незворотно.",
        cut: "Вирізати",
        copy: "Копіювати",
        paste: "Вставити",
        duplicate: "Дублювати",
        delete: "Видалити",
        copy_as_image: "Копіювати як зображення",
        view: "Перегляд",
        header: "Меню",
        sidebar: "Бічна панель",
        issues: "Проблеми",
        presentation_mode: "Режим презентації",
        strict_mode: "Строгий режим",
        field_details: "Деталі поля",
        reset_view: "Скинути перегляд",
        show_grid: "Показати сітку",
        show_cardinality: "Показати кардинальність",
        theme: "Тема",
        light: "Світла",
        dark: "Темна",
        zoom_in: "Збільшити",
        zoom_out: "Зменшити",
        fullscreen: "Повний екран",
        settings: "Налаштування",
        show_timeline: "Показати хронологію",
        autosave: "Автозбереження",
        panning: "Прокрутка",
        table_width: "Ширина таблиці",
        language: "Мова",
        flush_storage: "Очистити сховище",
        are_you_sure_flush_storage:
            "Ви впевнені, що хочете очистити сховище? Це незворотно видалить всі ваші діаграми та користувацькі шаблони.",
        storage_flushed: "Сховище очищено",
        help: "Допомога",
        shortcuts: "Гарячі клавіші",
        ask_on_discord: "Запитайте нас на Discord",
        feedback: "Відгук",
        no_changes: "Змін немає",
        loading: "Завантаження...",
        last_saved: "Останнє збереження",
        saving: "Збереження...",
        failed_to_save: "Не вдалося зберегти",
        fit_window_reset: "Підлаштувати під вікно / Скинути",
        zoom: "Масштаб",
        add_table: "Додати таблицю",
        add_area: "Додати область",
        add_note: "Додати нотатку",
        add_type: "Додати тип",
        to_do: "Зробити",
        tables: "Таблиці",
        relationships: "Зв'язки",
        subject_areas: "Тематичні області",
        notes: "Нотатки",
        types: "Типи",
        search: "Пошук...",
        no_tables: "Немає таблиць",
        no_tables_text: "Почніть створювати вашу діаграму!",
        no_relationships: "Немає зв'язків",
        no_relationships_text:
            "Перетягніть, щоб з'єднати поля та створити зв'язки!",
        no_subject_areas: "Немає тематичних областей",
        no_subject_areas_text: "Додайте тематичні області для групування таблиць!",
        no_notes: "Немає нотаток",
        no_notes_text: "Використовуйте нотатки для запису додаткової інформації",
        no_types: "Немає типів",
        no_types_text: "Створіть власні користувацькі типи даних",
        no_issues: "Проблем не виявлено.",
        strict_mode_is_on_no_issues:
            "Строгий режим вимкнено, тому проблеми не будуть відображатися.",
        name: "Назва",
        type: "Тип",
        null: "Null",
        not_null: "Не Null",
        primary: "Первинний",
        unique: "Унікальний",
        autoincrement: "Автоінкремент",
        default_value: "За замовчуванням",
        check: "Перевірка виразу",
        this_will_appear_as_is: "*Це з'явиться в згенерованому скрипті так, як є.",
        comment: "Коментар",
        add_field: "Додати поле",
        values: "Значення",
        size: "Розмір",
        precision: "Точність",
        set_precision: "Встановити точність: (розмір, цифри)",
        use_for_batch_input: "Використовуйте , для пакетного введення",
        indices: "Індекси",
        add_index: "Додати індекс",
        select_fields: "Виберіть поля",
        title: "Заголовок",
        not_set: "Не встановлено",
        foreign: "Зовнішній",
        cardinality: "Кардинальність",
        on_update: "При оновленні",
        on_delete: "При видаленні",
        swap: "Поміняти місцями",
        one_to_one: "Один до одного",
        one_to_many: "Один до багатьох",
        many_to_one: "Багато до одного",
        content: "Зміст",
        types_info:
            "Ця функція призначена для об'єктно-реляційних СУБД, таких як PostgreSQL.\nЯкщо використовується для MySQL або MariaDB, буде згенерований тип JSON з відповідною перевіркою JSON.\nЯкщо використовується для SQLite, буде перетворено на BLOB.\nЯкщо використовується для MSSQL, буде згенеровано тип-аліас для першого поля.",
        table_deleted: "Таблицю видалено",
        area_deleted: "Область видалено",
        note_deleted: "Нотатку видалено",
        relationship_deleted: "Зв'язок видалено",
        type_deleted: "Тип видалено",
        cannot_connect: "Неможливо з'єднати, стовпці мають різні типи",
        copied_to_clipboard: "Скопійовано до буфера обміну",
        create_new_diagram: "Створити нову діаграму",
        cancel: "Скасувати",
        open_diagram: "Відкрити діаграму",
        rename_diagram: "Перейменувати діаграму",
        export: "Експортувати",
        export_image: "Експортувати зображення",
        create: "Створити",
        confirm: "Підтвердити",
        last_modified: "Остання зміна",
        drag_and_drop_files:
            "Перетягніть файл сюди або натисніть для завантаження.",
        upload_sql_to_generate_diagrams:
            "Завантажте SQL файл для автоматичного створення ваших таблиць та стовпців.",
        overwrite_existing_diagram: "Перезаписати існуючу діаграму",
        only_mysql_supported:
            "*На даний момент підтримується завантаження лише MySQL скриптів.",
        blank: "Порожній",
        filename: "Назва файлу",
        table_w_no_name: "Оголошено таблицю без назви",
        duplicate_table_by_name: "Дубльована таблиця з назвою '{{tableName}}'",
        empty_field_name: "Порожнє поле `name` в таблиці '{{tableName}}'",
        empty_field_type: "Порожнє поле `type` в таблиці '{{tableName}}'",
        no_values_for_field:
            "'{{fieldName}}' поле в таблиці '{{tableName}}' має тип `{{type}}`, але значення не вказані",
        default_doesnt_match_type:
            "Значення за замовчуванням для поля '{{fieldName}}' в таблиці '{{tableName}}' не відповідає його типу",
        not_null_is_null:
            "'{{fieldName}}' поле в таблиці '{{tableName}}' є NOT NULL, але має значення за замовчуванням NULL",
        duplicate_fields:
            "Дубльовані поля таблиці з назвою '{{fieldName}}' в таблиці '{{tableName}}'",
        duplicate_index:
            "Дубльований індекс з назвою '{{indexName}}' в таблиці '{{tableName}}'",
        empty_index: "Індекс в таблиці '{{tableName}}' не індексує жодного стовпця",
        no_primary_key: "Таблиця '{{tableName}}' не має первинного ключа",
        type_with_no_name: "Оголошено тип без назви",
        duplicate_types: "Дубльовані типи з назвою '{{typeName}}'",
        type_w_no_fields: "Оголошено порожній тип '{{typeName}}' без полів",
        empty_type_field_name: "Порожнє поле `name` в типі '{{typeName}}'",
        empty_type_field_type: "Порожнє поле `type` в типі '{{typeName}}'",
        no_values_for_type_field:
            "'{{fieldName}}' поле в типі '{{typeName}}' має тип `{{type}}`, але значення не вказані",
        duplicate_type_fields:
            "Дубльовані поля типу з назвою '{{fieldName}}' в типі '{{typeName}}'",
        duplicate_reference: "Дубльована посилання з назвою '{{refName}}'",
        circular_dependency:
            "Циклічна залежність, що стосується таблиці '{{refName}}'",
        timeline: "Хронологія",
        priority: "Пріоритет",
        none: "Жодний",
        low: "Низький",
        medium: "Середній",
        high: "Високий",
        sort_by: "Сортувати за",
        my_order: "Мій порядок",
        completed: "Завершено",
        alphabetically: "Алфавітно",
        add_task: "Додати завдання",
        details: "Деталі",
        no_tasks: "У вас поки немає завдань.",
        no_activity: "У вас поки немає активності.",
        move_element: "Перемістити {{name}} до {{coords}}",
        edit_area: "{{extra}} Редагувати область {{areaName}}",
        delete_area: "Видалити область {{areaName}}",
        edit_note: "{{extra}} Редагувати нотатку {{noteTitle}}",
        delete_note: "Видалити нотатку {{noteTitle}}",
        edit_table: "{{extra}} Редагувати таблицю {{tableName}}",
        delete_table: "Видалити таблицю {{tableName}}",
        edit_type: "{{extra}} Редагувати тип {{typeName}}",
        delete_type: "Видалити тип {{typeName}}",
        add_relationship: "Додати зв'язок",
        edit_relationship: "{{extra}} Редагувати зв'язок {{refName}}",
        delete_relationship: "Видалити зв'язок {{refName}}",
        not_found: "Не знайдено",
    },
};

export {uk, ukrainian};
