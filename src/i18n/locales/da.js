const danish = {
    name: "Danish",
    native_name: "<PERSON><PERSON>",
    code: "da",
};

const da = {
    translation: {
        report_bug: "Rapportér en fejl",
        import_from: "Importér",
        import: "Import<PERSON><PERSON>",
        file: "Fil",
        new: "<PERSON>y",
        new_window: "Nyt vindue",
        open: "<PERSON><PERSON>",
        save: "Gem",
        save_as: "Gem som",
        save_as_template: "Gem som skabelon",
        template_saved: "Skabelon gemt!",
        rename: "Omdø<PERSON>",
        delete_diagram: "Slet diagram",
        are_you_sure_delete_diagram:
            "Er du sikker på at du vil slette dette diagram? <PERSON>ne handling er irreversibel.",
        oops_smth_went_wrong: "Ups! Noget gik galt.",
        import_diagram: "Importér diagram",
        import_from_source: "Importér fra kilde",
        export_as: "Eksportér som",
        export_source: "Eksportér kilde",
        models: "<PERSON><PERSON>",
        exit: "<PERSON><PERSON>lut",
        edit: "<PERSON>ig<PERSON><PERSON>",
        undo: "<PERSON><PERSON><PERSON>",
        redo: "<PERSON><PERSON>",
        clear: "<PERSON>yd",
        are_you_sure_clear:
            "Er du sikker på at du vil rydde diagrammet? <PERSON><PERSON> handling er irreversibel.",
        cut: "Klip",
        copy: "Kopiér",
        paste: "Indsæt",
        duplicate: "Duplikér",
        delete: "Slet",
        copy_as_image: "Kopiér som billede",
        view: "Visning",
        header: "Hoved",
        sidebar: "Sidebar",
        issues: "Problemer",
        presentation_mode: "Præsentations tilstand",
        strict_mode: "Streng tilstand",
        field_details: "Felt detaljer",
        reset_view: "Nulstil visning",
        show_grid: "Vis gitter",
        show_cardinality: "Vis kardinalitet",
        theme: "Tema",
        light: "Lyst",
        dark: "Mørkt",
        zoom_in: "Zoom ind",
        zoom_out: "Zoom ud",
        fullscreen: "Fuld skærm",
        settings: "Indstillinger",
        show_timeline: "Vis tidslinje",
        autosave: "Gem automatisk",
        panning: "Panorering",
        table_width: "Tabel bredde",
        language: "Sprog",
        flush_storage: "Tøm lagring",
        are_you_sure_flush_storage:
            "Er du sikker på at du vil tømme lagringen? Dette gør at alle dine diagrammer og individuelle skabeloner bliver slettet irreversibelt",
        storage_flushed: "Lagring tømt",
        help: "Hjælp",
        shortcuts: "Genveje",
        ask_on_discord: "Spørg os på Discord",
        feedback: "Feedback",
        no_changes: "Ingen ændringer",
        loading: "Loader...",
        last_saved: "Sidst gemt",
        saving: "Gemmer...",
        failed_to_save: "Gem fejlede",
        fit_window_reset: "Tilpas vindue / Nulstil",
        zoom: "Zoom",
        add_table: "Tilføj tabel",
        add_area: "Tilføj område",
        add_note: "Tilføj note",
        add_type: "Tilføj type",
        to_do: "To-do",
        tables: "Tabeller",
        relationships: "Forhold",
        subject_areas: "Emne områder",
        notes: "Noter",
        types: "Typer",
        search: "Søg...",
        no_tables: "Ingen tabeller",
        no_tables_text: "Begynd at bygge dit diagram!",
        no_relationships: "Ingen forhold",
        no_relationships_text: "Træk for at forbinde felter og dan forhold!",
        no_subject_areas: "Ingen emne områder",
        no_subject_areas_text: "Tilføj emne områder for at gruppere tabeller!",
        no_notes: "Ingen noter",
        no_notes_text: "Brug noter for at registrere ekstra info",
        no_types: "Ingen typer",
        no_types_text: "Lav dine egne tilpassede data typer",
        no_issues: "Ingen problemer blev opdaget.",
        strict_mode_is_on_no_issues:
            "Streng tilstand er slået fra, så ingen problemer vil blive vist.",
        name: "Navn",
        type: "Type",
        null: "Nul",
        not_null: "Ikke nul",
        primary: "Primær",
        unique: "Unik",
        autoincrement: "Auto-inkrementel",
        default_value: "Standardværdi",
        check: "Tjek udtryk",
        this_will_appear_as_is:
            "*Dette vil fremstå i det generede script som det er.",
        comment: "Kommentar",
        add_field: "Tilføj felt",
        values: "værdier",
        size: "Størrelse",
        precision: "Præcision",
        set_precision: "Sæt præcision: (størrelse, cifre)",
        use_for_batch_input: "Brug , for samlet indtastning",
        indices: "Indekser",
        add_index: "Tilføj indeks",
        select_fields: "Vælg felter",
        title: "Titel",
        not_set: "Ikke sat",
        foreign: "Fremmed",
        cardinality: "Kardinalitet",
        on_update: "På opdater",
        on_delete: "På slet",
        swap: "Swap",
        one_to_one: "En til en",
        one_to_many: "En til mange",
        many_to_one: "Mange til en",
        content: "Indhold",
        types_info:
            "Denne feature er ment til objekt-relationelle DBMSer ligesom PostgreSQL.\nHvis brugt til MySQL eller MariaDB, vil en JSON type blive genereret med tilsvarende JSON validerings-tjek.\nHvis brugt til SQLite, vil det blive oversat til en BLOB.\nHvis brugt til MSSQL vil et type-alias til det første felt blive genereret.",
        table_deleted: "Tabel slettet",
        area_deleted: "Område slettet",
        note_deleted: "Note slettet",
        relationship_deleted: "Forhold slettet",
        type_deleted: "Type slettet",
        cannot_connect: "Kan ikke forbinde, kolonnerne har forskellige typer",
        copied_to_clipboard: "Kopiér til clipboard",
        create_new_diagram: "Opret nyt diagram",
        cancel: "Afbryd",
        open_diagram: "Åben diagram",
        rename_diagram: "Omdøb diagram",
        export: "Eksportér",
        export_image: "Eksportér billede",
        create: "Opret",
        confirm: "Bekræft",
        last_modified: "Sidst ændret",
        drag_and_drop_files: "Træk og drop filen her eller klik for at uploade.",
        upload_sql_to_generate_diagrams:
            "Upload en sql fil for at auto-generere dine tabeller og kolonner.",
        overwrite_existing_diagram: "Overskriv eksisterende diagram",
        only_mysql_supported:
            "*For tiden er det kun MySQL scripts der er understøttet.",
        blank: "Blank",
        filename: "Filnavn",
        table_w_no_name: "Erklærede en tabel med intet navn",
        duplicate_table_by_name: "Dupliker tabellen med navnet '{{tableName}}'",
        empty_field_name: "Tomt felt `navn` i tabellen '{{tableName}}'",
        empty_field_type: "Tomt felt `type` i tabellen '{{tableName}}'",
        no_values_for_field:
            "'{{fieldName}}' felt fra tabellen '{{tableName}}' er af type `{{type}}` men ingen værdi er blevet specificeret",
        default_doesnt_match_type:
            "Standardværdien for feltet '{{fieldName}}' i tabellen '{{tableName}}' stemmer ikke overens med dens type",
        not_null_is_null:
            "'{{fieldName}}' felt fra tabellen '{{tableName}}' er IKKE NUL, men har standardværdien NUL",
        duplicate_fields:
            "Duplikat tabel felter med navn '{{fieldName}}' på tabellen '{{tableName}}'",
        duplicate_index:
            "Duplikat indeks med navn '{{indexName}}' på tabellen '{{tableName}}'",
        empty_index: "Indeks på tabel '{{tableName}}' indekser ingen kolonner",
        no_primary_key: "Tabel '{{tableName}}' har ingen primær nøgle",
        type_with_no_name: "Erklæret en type med intet navn",
        duplicate_types: "Duplikat typer med navnet '{{typeName}}'",
        type_w_no_fields: "Erklæret en tom type '{{typeName}}' med ingen felter",
        empty_type_field_name: "Tomt felt `navn` på typen '{{typeName}}'",
        empty_type_field_type: "Tomt felt `type` på typen '{{typeName}}'",
        no_values_for_type_field:
            "'{{fieldName}}' felt af typen '{{typeName}}' er af typen `{{type}}` men ingen værdier er blevet specificeret",
        duplicate_type_fields:
            "Duplikat type felter med navnet '{{fieldName}}' på typen '{{typeName}}'",
        duplicate_reference: "Duplikat reference med navnet '{{refName}}'",
        circular_dependency: "Cirkulær afhængighed involveret tabel '{{refName}}'",
        timeline: "Tidslinje",
        priority: "Prioritet",
        none: "Ingen",
        low: "Lav",
        medium: "Middel",
        high: "Høj",
        sort_by: "Sortér på",
        my_order: "Min prioritering",
        completed: "Færdiggjort",
        alphabetically: "Alfabetisk",
        add_task: "Tilføj opgave",
        details: "Detaljer",
        no_tasks: "Du har ingen opgaver endnu.",
        no_activity: "Du har ingen aktivitet endnu.",
        move_element: "Flyt {{name}} til {{coords}}",
        edit_area: "{{extra}} Redigér område {{areaName}}",
        delete_area: "Slet område {{areaName}}",
        edit_note: "{{extra}} Redigér note {{noteTitle}}",
        delete_note: "Slet note {{noteTitle}}",
        edit_table: "{{extra}} Redigér tabel {{tableName}}",
        delete_table: "Slet tabel {{tableName}}",
        edit_type: "{{extra}} Redigér type {{typeName}}",
        delete_type: "Slet type {{typeName}}",
        add_relationship: "Tilføj forhold",
        edit_relationship: "{{extra}} Redigér forhold {{refName}}",
        delete_relationship: "Slet forhold {{refName}}",
        not_found: "Ikke fundet",
    },
};

export {da, danish};
