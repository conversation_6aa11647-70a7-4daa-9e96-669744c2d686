import {useEffect, useState} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {getAllDocs, getDefaultDoc, getDocByKey} from '../utils/docsParser';
import {useTranslation} from 'react-i18next';

// 简单的 Markdown 渲染组件
function MarkdownRenderer({content}) {
    // 简单的 markdown 转换函数
    const renderMarkdown = (text) => {
        // 处理引用块（blockquote）- 需要在其他处理之前
        text = text.replace(/^> (.+)$/gim, '<blockquote class="border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 italic">$1</blockquote>');

        // 处理多行引用块
        text = text.replace(/^>\s*$/gim, '<br>');

        // 处理图片
        text = text.replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img src="/src/assets/docs/$2" alt="$1" class="max-w-full h-auto my-4 rounded shadow-sm">');

        // 处理标题
        text = text.replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>');
        text = text.replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mt-6 mb-3">$1</h2>');
        text = text.replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>');

        // 处理粗体
        text = text.replace(/\*\*(.*)\*\*/gim, '<strong class="font-semibold">$1</strong>');

        // 处理斜体
        text = text.replace(/\*(.*)\*/gim, '<em class="italic">$1</em>');

        // 处理代码块
        text = text.replace(/```([^`]+)```/gim, '<pre class="bg-gray-100 p-3 rounded mt-2 mb-2 overflow-x-auto"><code>$1</code></pre>');

        // 处理行内代码
        text = text.replace(/`([^`]+)`/gim, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>');

        // 处理链接
        text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" class="text-blue-600 hover:underline">$1</a>');

        // 处理表格
        const tableRegex = /\|(.+)\|\n\|(.+)\|\n((?:\|.+\|\n?)*)/gim;
        text = text.replace(tableRegex, (match, header, separator, rows) => {
            const headerCells = header.split('|').map(cell => cell.trim()).filter(cell => cell);
            const rowsArray = rows.trim().split('\n').map(row =>
                row.split('|').map(cell => cell.trim()).filter(cell => cell)
            );

            let tableHtml = '<table class="min-w-full border-collapse border border-gray-300 mt-4 mb-4">';
            tableHtml += '<thead><tr>';
            headerCells.forEach(cell => {
                tableHtml += `<th class="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold">${cell}</th>`;
            });
            tableHtml += '</tr></thead><tbody>';

            rowsArray.forEach(row => {
                tableHtml += '<tr>';
                row.forEach(cell => {
                    tableHtml += `<td class="border border-gray-300 px-4 py-2">${cell}</td>`;
                });
                tableHtml += '</tr>';
            });

            tableHtml += '</tbody></table>';
            return tableHtml;
        });

        // 处理段落
        text = text.replace(/\n\n/gim, '</p><p class="mb-4">');
        text = '<p class="mb-4">' + text + '</p>';

        // 清理空段落
        text = text.replace(/<p class="mb-4"><\/p>/gim, '');

        return text;
    };

    return (
        <div
            className="prose max-w-none"
            dangerouslySetInnerHTML={{__html: renderMarkdown(content)}}
        />
    );
}

export default function DocsPage() {
    const {docName} = useParams();
    const navigate = useNavigate();
    const {t} = useTranslation();
    const [docs, setDocs] = useState([]);
    const [currentDoc, setCurrentDoc] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // 获取所有文档
        const allDocs = getAllDocs();
        setDocs(allDocs);

        // 确定当前要显示的文档
        let docToShow;
        if (docName) {
            docToShow = getDocByKey(docName);
            if (!docToShow) {
                // 如果指定的文档不存在，重定向到默认文档
                const defaultDoc = getDefaultDoc();
                if (defaultDoc) {
                    navigate(`/docs/${defaultDoc.key}`, {replace: true});
                    return;
                }
            }
        } else {
            // 如果没有指定文档，显示默认文档
            docToShow = getDefaultDoc();
            if (docToShow) {
                navigate(`/docs/${docToShow.key}`, {replace: true});
                return;
            }
        }

        setCurrentDoc(docToShow);
        setLoading(false);
    }, [docName, navigate]);

    const handleDocClick = (doc) => {
        navigate(`/docs/${doc.key}`);
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="text-lg">Loading...</div>
            </div>
        );
    }

    if (!currentDoc) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="text-lg">Document not found</div>
            </div>
        );
    }

    return (
        <div className="flex h-screen bg-white">
            {/* 侧边栏 */}
            <div className="w-64 bg-gray-50 border-r border-gray-200 overflow-y-auto">
                <div className="p-4">
                    <h2 className="text-lg font-semibold text-gray-900 mb-4">Documentation</h2>
                    <nav className="space-y-1">
                        {docs.map((doc) => (
                            <button
                                key={doc.key}
                                onClick={() => handleDocClick(doc)}
                                className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                                    currentDoc.key === doc.key
                                        ? 'bg-blue-100 text-blue-700 font-medium'
                                        : 'text-gray-700 hover:bg-gray-100'
                                }`}
                            >
                                {doc.title}
                            </button>
                        ))}
                    </nav>
                </div>
            </div>

            {/* 主内容区域 */}
            <div className="flex-1 overflow-y-auto">
                <div className="max-w-4xl mx-auto px-6 py-8">
                    <MarkdownRenderer content={currentDoc.content}/>
                </div>
            </div>
        </div>
    );
}
