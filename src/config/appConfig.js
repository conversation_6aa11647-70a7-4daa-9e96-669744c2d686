// 应用配置文件
// 这里定义了应用的各种功能开关和配置项

/**
 * 应用功能配置
 */
export const appConfig = {
    // 分享功能配置
    features: {
        // 是否启用分享功能
        // 如果设置为 false，用户将无法看到和使用分享按钮
        enableShare: import.meta.env.VITE_ENABLE_SHARE !== 'false', // 默认启用，除非明确设置为 false

        // 其他功能开关可以在这里添加
        // enableTemplates: true,
        // enableExport: true,
    },

    // API 配置
    api: {
        // 后端 URL（用于分享功能）
        backendUrl: import.meta.env.VITE_BACKEND_URL || '',

        // GitHub Access Token（用于分享功能）
        githubAccessToken: import.meta.env.VITE_GITHUB_ACCESS_TOKEN || '',
    },

    // UI 配置
    ui: {
        // 默认主题
        defaultTheme: 'light',

        // 默认语言
        defaultLanguage: 'en',
    }
};

/**
 * 检查分享功能是否可用
 * 需要同时满足：配置启用 + 有必要的环境变量
 */
export function isShareFeatureAvailable() {
    return appConfig.features.enableShare &&
        (appConfig.api.backendUrl || appConfig.api.githubAccessToken);
}

/**
 * 获取功能配置
 */
export function getFeatureConfig(featureName) {
    return appConfig.features[featureName] || false;
}

/**
 * 检查环境变量配置
 */
export function checkEnvironmentConfig() {
    const warnings = [];

    if (appConfig.features.enableShare) {
        if (!appConfig.api.backendUrl && !appConfig.api.githubAccessToken) {
            warnings.push('Share feature is enabled but no backend URL or GitHub token is configured');
        }
    }

    return warnings;
}
