@import "tailwindcss";

@config '../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@layer base {
    /*
     * Workaround to allow proper bottom positioning for fixed
     * elements in mobile browsers with collapsing url bars:
     * https://stackoverflow.com/a/17555766/1137077
     */
    :root,
    html,
    body {
        @apply h-full min-h-full;
    }

    #root {
        @apply h-full min-h-full;
    }
}

.semi-form-vertical .semi-form-field {
    margin: 0;
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    overflow: hidden;
}

.semi-card .semi-collapse-item {
    border: none !important;
}

.semi-button.semi-button-light {
    background-color: rgba(var(--semi-grey-1), 1);
}

.semi-button.semi-button-light:hover {
    background-color: rgba(var(--semi-grey-2), 1);
}

.semi-button.semi-button-primary:not(.semi-button-light):not(
    .semi-button-borderless
  ) {
    background-color: rgba(var(--semi-blue-5), 1);
}

.semi-button.semi-button-primary:not(.semi-button-light):not(
    .semi-button-borderless
  ):hover {
    background-color: rgba(var(--semi-blue-6), 1);
}

.semi-spin-wrapper {
    color: inherit;
}

:disabled {
    /* inherit Semi's cursor style for disabled elements */
    cursor: inherit;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    opacity: 0;
}

::-webkit-scrollbar-corner {
    opacity: 0;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(var(--semi-grey-2), 1);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--semi-grey-3), 1);
}

.menu::-webkit-scrollbar-thumb {
    background-color: rgba(var(--semi-grey-4), 1);
}

.theme {
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-bg-0);
}

.sidesheet-theme {
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-bg-2);
}

.popover-theme {
    color: var(--semi-color-text-1);
    background-color: var(--semi-color-bg-3);
}

.card-theme {
    color: var(--semi-color-text-1);
    background-color: rgba(var(--semi-grey-0), 1);
}

.toolbar-theme {
    background-color: rgba(var(--semi-grey-1), 0.7);
}

.hover-1:hover {
    background-color: rgba(var(--semi-grey-1), 1);
}

.bg-semi-grey-2 {
    background-color: rgba(var(--semi-grey-2), 1);
}

.hover-2:hover {
    background-color: rgba(var(--semi-grey-2), 1);
}

.text-color {
    color: var(--semi-color-text-1);
}

.border-color {
    border-color: rgba(var(--semi-grey-9), 0.08);
}

.table-border {
    border-color: rgba(var(--semi-grey-2), 1);
}

.bg-dots {
    background-color: white;
    opacity: 0.8;
    background-image: radial-gradient(rgb(118, 118, 209) 1px, white 1px);
    background-size: 20px 20px;
}

.sliding-vertical span {
    animation: top-to-bottom 9s linear infinite 0s;
    -ms-animation: top-to-bottom 9s linear infinite 0s;
    -webkit-animation: top-to-bottom 9s linear infinite 0s;
    opacity: 0;
    overflow: hidden;
    position: absolute;
}

.sliding-vertical span:nth-child(2) {
    animation-delay: 3s;
    -ms-animation-delay: 3s;
    -webkit-animation-delay: 3s;
}

.sliding-vertical span:nth-child(3) {
    animation-delay: 6s;
    -ms-animation-delay: 6s;
    -webkit-animation-delay: 6s;
}

@keyframes top-to-bottom {
    0% {
        opacity: 0;
    }
    5% {
        opacity: 0;
        transform: translateY(-18px);
    }
    10% {
        opacity: 1;
        transform: translateY(0px);
    }
    25% {
        opacity: 1;
        transform: translateY(0px);
    }
    30% {
        opacity: 0;
        transform: translateY(18px);
    }
    80% {
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}
