import {createContext, useEffect, useState} from "react";
import {tableWidth} from "../data/constants";

const defaultSettings = {
    strictMode: false,
    showFieldSummary: true,
    showGrid: true,
    snapToGrid: false,
    showDataTypes: true,
    mode: "light",
    autosave: true,
    showCardinality: true,
    showRelationshipLabels: true,
    tableWidth: tableWidth,
    showDebugCoordinates: false,
    enableShare: true, // 控制分享功能的开关
};

export const SettingsContext = createContext(defaultSettings);

export default function SettingsContextProvider({children}) {
    const [settings, setSettings] = useState(defaultSettings);

    useEffect(() => {
        const settings = localStorage.getItem("settings");
        if (settings) {
            setSettings(JSON.parse(settings));
        }
    }, []);

    useEffect(() => {
        document.body.setAttribute("theme-mode", settings.mode);
    }, [settings.mode]);

    useEffect(() => {
        localStorage.setItem("settings", JSON.stringify(settings));
    }, [settings]);

    return (
        <SettingsContext.Provider value={{settings, setSettings}}>
            {children}
        </SettingsContext.Provider>
    );
}
