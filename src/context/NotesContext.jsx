import {createContext, useState} from "react";
import {Action, ObjectType, defaultNoteTheme} from "../data/constants";
import {useUndoRedo, useTransform, useSelect} from "../hooks";
import {Toast} from "@douyinfe/semi-ui";
import {useTranslation} from "react-i18next";

export const NotesContext = createContext(null);

export default function NotesContextProvider({children}) {
    const {t} = useTranslation();
    const [notes, setNotes] = useState([]);
    const {transform} = useTransform();
    const {setUndoStack, setRedoStack} = useUndoRedo();
    const {selectedElement, setSelectedElement} = useSelect();

    const addNote = (data, addToHistory = true) => {
        if (data) {
            setNotes((prev) => {
                const temp = prev.slice();
                temp.splice(data.id, 0, data);
                return temp.map((t, i) => ({...t, id: i}));
            });
        } else {
            const height = 88;
            setNotes((prev) => [
                ...prev,
                {
                    id: prev.length,
                    x: transform.pan.x,
                    y: transform.pan.y - height / 2,
                    title: `note_${prev.length}`,
                    content: "",
                    locked: false,
                    color: defaultNoteTheme,
                    height,
                },
            ]);
        }
        if (addToHistory) {
            setUndoStack((prev) => [
                ...prev,
                {
                    action: Action.ADD,
                    element: ObjectType.NOTE,
                    message: t("add_note"),
                },
            ]);
            setRedoStack([]);
        }
    };

    const deleteNote = (id, addToHistory = true) => {
        if (addToHistory) {
            Toast.success(t("note_deleted"));
            setUndoStack((prev) => [
                ...prev,
                {
                    action: Action.DELETE,
                    element: ObjectType.NOTE,
                    data: notes[id],
                    message: t("delete_note", {noteTitle: notes[id].title}),
                },
            ]);
            setRedoStack([]);
        }
        setNotes((prev) =>
            prev.filter((e) => e.id !== id).map((e, i) => ({...e, id: i})),
        );
        if (id === selectedElement.id) {
            setSelectedElement((prev) => ({
                ...prev,
                element: ObjectType.NONE,
                id: -1,
                open: false,
            }));
        }
    };

    const updateNote = (id, values) => {
        setNotes((prev) =>
            prev.map((t) => {
                if (t.id === id) {
                    return {
                        ...t,
                        ...values,
                    };
                }
                return t;
            }),
        );
    };

    return (
        <NotesContext.Provider
            value={{
                notes,
                setNotes,
                updateNote,
                addNote,
                deleteNote,
                notesCount: notes.length,
            }}
        >
            {children}
        </NotesContext.Provider>
    );
}
