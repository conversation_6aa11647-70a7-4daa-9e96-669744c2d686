import {BrowserRouter, Route, Routes, useLocation} from "react-router-dom";
import {useLayoutEffect} from "react";
import Editor from "./pages/Editor";
import LandingPage from "./pages/LandingPage";
import DocsPage from "./pages/DocsPage";
import SettingsContextProvider from "./context/SettingsContext";
import NotFound from "./pages/NotFound";

export default function App() {
    return (
        <SettingsContextProvider>
            <BrowserRouter>
                <RestoreScroll/>
                <Routes>
                    <Route path="/" element={<LandingPage/>}/>
                    <Route path="/editor" element={<Editor/>}/>
                    <Route path="/docs" element={<DocsPage/>}/>
                    <Route path="/docs/:docName" element={<DocsPage/>}/>
                    <Route path="*" element={<NotFound/>}/>
                </Routes>
            </BrowserRouter>
        </SettingsContextProvider>
    );
}

function RestoreScroll() {
    const location = useLocation();
    useLayoutEffect(() => {
        window.scroll(0, 0);
    }, [location.pathname]);
    return null;
}
