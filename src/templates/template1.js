import {Cardinality} from "../data/constants";

export const template1 = {
    tables: [
        {
            id: 0,
            name: "users",
            x: 114.92525,
            y: 281.2977500000002,
            fields: [
                {
                    name: "id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: true,
                    unique: true,
                    notNull: true,
                    increment: true,
                    comment: "",
                    id: 0,
                },
                {
                    name: "username",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 1,
                    size: 255,
                },
                {
                    name: "password",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 2,
                    size: 255,
                },
                {
                    name: "email",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 3,
                    size: 255,
                },
                {
                    name: "last_login",
                    type: "TIMESTAMP",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 4,
                    size: "",
                    values: [],
                },
            ],
            comment: "",
            indices: [],
            color: "#6360f7",
        },
        {
            id: 1,
            name: "blog_posts",
            x: 277.57925,
            y: 19.206750000000113,
            fields: [
                {
                    name: "id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: true,
                    unique: true,
                    notNull: true,
                    increment: true,
                    comment: "",
                    id: 0,
                },
                {
                    name: "user_id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 1,
                },
                {
                    name: "title",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 2,
                    size: 255,
                },
                {
                    name: "content",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 3,
                    size: 255,
                },
                {
                    name: "cover",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 4,
                    size: 255,
                },
            ],
            comment: "",
            indices: [],
            color: "#bc49c4",
        },
        {
            id: 2,
            name: "comments",
            x: 505.62112500000035,
            y: 341.6078750000002,
            fields: [
                {
                    name: "id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: true,
                    unique: true,
                    notNull: true,
                    increment: true,
                    comment: "",
                    id: 0,
                },
                {
                    name: "blog_id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 1,
                },
                {
                    name: "user_id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 2,
                },
                {
                    name: "content",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 3,
                    size: 255,
                },
            ],
            comment: "",
            indices: [],
            color: "#3cde7d",
        },
        {
            id: 3,
            name: "tags",
            x: 743.2832500000009,
            y: 318.1841250000001,
            fields: [
                {
                    name: "id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: true,
                    unique: true,
                    notNull: true,
                    increment: true,
                    comment: "",
                    id: 0,
                },
                {
                    name: "name",
                    type: "VARCHAR",
                    default: "",
                    check: "",
                    primary: false,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 1,
                    size: 255,
                },
            ],
            comment: "",
            indices: [],
            color: "#7d9dff",
        },
        {
            id: 4,
            name: "blog_tag",
            x: 812.1175000000004,
            y: 131.55062500000008,
            fields: [
                {
                    name: "blog_id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: true,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 0,
                },
                {
                    name: "tag_id",
                    type: "INT",
                    default: "",
                    check: "",
                    primary: true,
                    unique: false,
                    notNull: false,
                    increment: false,
                    comment: "",
                    id: 1,
                },
            ],
            comment: "",
            indices: [],
            color: "#ffe159",
        },
    ],
    relationships: [
        {
            startTableId: 1,
            startFieldId: 1,
            endTableId: 0,
            endFieldId: 0,
            name: "blog_posts_user_id_fk",
            cardinality: Cardinality.MANY_TO_ONE,
            updateConstraint: "No action",
            deleteConstraint: "No action",
            id: 0,
        },
        {
            startTableId: 2,
            startFieldId: 1,
            endTableId: 1,
            endFieldId: 0,
            name: "comments_blog_id_fk",
            cardinality: Cardinality.MANY_TO_ONE,
            updateConstraint: "No action",
            deleteConstraint: "No action",
            id: 1,
        },
        {
            startTableId: 2,
            startFieldId: 2,
            endTableId: 0,
            endFieldId: 0,
            name: "comments_user_id_fk",
            cardinality: Cardinality.MANY_TO_ONE,
            updateConstraint: "No action",
            deleteConstraint: "No action",
            id: 2,
        },
        {
            startTableId: 4,
            startFieldId: 1,
            endTableId: 3,
            endFieldId: 0,
            name: "blog_tag_tag_id_fk",
            cardinality: Cardinality.MANY_TO_ONE,
            updateConstraint: "No action",
            deleteConstraint: "No action",
            id: 3,
        },
        {
            startTableId: 4,
            startFieldId: 0,
            endTableId: 1,
            endFieldId: 0,
            name: "blog_tag_blog_id_fk",
            cardinality: Cardinality.MANY_TO_ONE,
            updateConstraint: "No action",
            deleteConstraint: "No action",
            id: 4,
        },
    ],
    notes: [],
    subjectAreas: [],
    types: [],
    title: "Blog database schema",
    description:
        "A blog database including tables such as posts, users, comments, and tags to facilitate the storage and retrieval of blog-related information.",
    custom: 0,
};
