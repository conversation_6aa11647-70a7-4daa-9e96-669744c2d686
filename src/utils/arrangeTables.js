import {
    tableColorStripHeight,
    tableFieldHeight,
    tableHeaderHeight,
} from "../data/constants";

export function arrangeTables(diagram) {
    let maxHeight = -1;
    const tableWidth = 200;
    const gapX = 54;
    const gapY = 40;
    diagram.tables.forEach((table, i) => {
        if (i < diagram.tables.length / 2) {
            table.x = i * tableWidth + (i + 1) * gapX;
            table.y = gapY;
            const height =
                table.fields.length * tableFieldHeight +
                tableHeaderHeight +
                tableColorStripHeight;
            maxHeight = Math.max(height, maxHeight);
        } else {
            const index = diagram.tables.length - i - 1;
            table.x = index * tableWidth + (index + 1) * gapX;
            table.y = maxHeight + 2 * gapY;
        }
    });
}
