// 文档解析工具函数

// 使用 Vite 的 import.meta.glob 动态导入所有 markdown 文件
const docModules = import.meta.glob('../assets/docs/*.md', {
    query: '?raw',
    import: 'default',
    eager: true
});

// 缓存解析后的文档数据
let cachedDocs = null;

/**
 * 解析 markdown 文档的 frontmatter
 * @param {string} content - markdown 内容
 * @returns {object} - 包含 frontmatter 和 content 的对象
 */
export function parseFrontmatter(content) {
    const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
    const match = content.match(frontmatterRegex);

    if (!match) {
        return {frontmatter: {}, content};
    }

    const frontmatterText = match[1];
    const markdownContent = match[2];

    // 简单解析 YAML frontmatter
    const frontmatter = {};
    frontmatterText.split('\n').forEach(line => {
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
            const key = line.substring(0, colonIndex).trim();
            const value = line.substring(colonIndex + 1).trim();

            // 处理数字类型
            if (!isNaN(value) && value !== '') {
                frontmatter[key] = parseInt(value);
            } else {
                frontmatter[key] = value;
            }
        }
    });

    return {frontmatter, content: markdownContent};
}

/**
 * 提取文档标题（第一个 # 标题）
 * @param {string} content - markdown 内容
 * @returns {string} - 文档标题
 */
export function extractTitle(content) {
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1].trim() : 'Untitled';
}

/**
 * 从文件路径提取文档 key
 * @param {string} filePath - 文件路径
 * @returns {string} - 文档 key
 */
function extractDocKey(filePath) {
    const fileName = filePath.split('/').pop();
    return fileName.replace('.md', '');
}

/**
 * 获取所有文档信息
 * @returns {Array} - 文档信息数组，按 sidebar_position 排序
 */
export function getAllDocs() {
    // 如果已经缓存，直接返回
    if (cachedDocs) {
        return cachedDocs;
    }

    const docs = [];

    // 遍历所有导入的文档模块
    Object.entries(docModules).forEach(([filePath, content]) => {
        const key = extractDocKey(filePath);
        const {frontmatter, content: markdownContent} = parseFrontmatter(content);
        const title = extractTitle(markdownContent);

        docs.push({
            key,
            title,
            content: markdownContent,
            sidebarPosition: frontmatter.sidebar_position || 999,
            slug: frontmatter.slug || `/${key}`,
        });
    });

    // 按 sidebar_position 排序
    cachedDocs = docs.sort((a, b) => a.sidebarPosition - b.sidebarPosition);
    return cachedDocs;
}

/**
 * 根据 key 获取特定文档
 * @param {string} docKey - 文档 key
 * @returns {object|null} - 文档信息对象
 */
export function getDocByKey(docKey) {
    const allDocs = getAllDocs();
    return allDocs.find(doc => doc.key === docKey) || null;
}

/**
 * 获取默认文档（sidebar_position 最小的）
 * @returns {object} - 默认文档信息
 */
export function getDefaultDoc() {
    const allDocs = getAllDocs();
    return allDocs[0] || null;
}

/**
 * 清除缓存（用于开发时热更新）
 */
export function clearCache() {
    cachedDocs = null;
}
