import {
    closestCenter,
    DndContext,
    PointerSensor,
    useSensor,
    useSensors,
} from "@dnd-kit/core";
import {
    SortableContext,
    arrayMove,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {SortableItem} from "./SortableItem";

export function SortableList({
                                 items,
                                 onChange,
                                 afterChange,
                                 renderItem,
                                 keyPrefix,
                             }) {
    const sensors = useSensors(useSensor(PointerSensor));

    const handleDragEnd = (event) => {
        const {active, over} = event;

        if (active.id !== over.id) {
            const oldIndex = items.findIndex((item) => item.id === active.id);
            const newIndex = items.findIndex((item) => item.id === over.id);
            const newItems = arrayMove(items, oldIndex, newIndex);
            onChange(newItems);
            afterChange();
        }
    };

    return (
        <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
        >
            <SortableContext items={items} strategy={verticalListSortingStrategy}>
                {items.map((item, i) => (
                    <SortableItem
                        id={item.id}
                        key={`${keyPrefix}-sortable-item-${item.id}`}
                    >
                        {renderItem(item, i)}
                    </SortableItem>
                ))}
            </SortableContext>
        </DndContext>
    );
}
