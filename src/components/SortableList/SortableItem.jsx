import {useSortable} from "@dnd-kit/sortable";
import {CSS} from "@dnd-kit/utilities";

export function SortableItem({children, id}) {
    const {attributes, setNodeRef, transform, transition} = useSortable({
        id,
    });
    const style = {
        transform: CSS.Translate.toString(transform),
        transition,
    };

    return (
        <div ref={setNodeRef} style={style} {...attributes}>
            {children}
        </div>
    );
}
