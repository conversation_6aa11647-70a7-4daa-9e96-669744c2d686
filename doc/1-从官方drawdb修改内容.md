## 具体步骤

```
# 下载项目
git clone https://github.com/drawdb-io/drawdb
## 使用 main分支
# 开始自适应修改
## 删除文件：
public/rebots.txt
src/assets/warp.png
src/assets/discord.png
vercel.json
## 新增文件：
.github/workflows/sync.yml 【当仅仅只是修改本地内容，而非直接fork上游仓库，该脚本就没有必要使用】

## 修改文件：
.github/workflows/build.yml
src/App.jsx
src/main.jsx
src/pages/LandingPage.jsx
src/components/EditorHeader/ControlPanel.jsx
src/components/Navbar.jsx
src/pages/BugReport.jsx
src/pages/NotFound.jsx
index.html
README.md

## 移动文件
.github/workflows/sync.yml移动到上一层
```

> 注意 package.json和package-lock.json文件就仅仅使用其官方自带的即可，不需要修改

### 修改文件内容

#### build.yml

```
删除或者注释掉 - name: Run eslint run: npm run lint  
代码质量检查不行
```

#### App.jsx

```
去除：
import Survey from "./pages/Survey";
import BugReport from "./pages/BugReport";
【对应组件也需要去除】：
path="/survey" */
path="/bug-report" */
Survey、BugReport、Templates 组件删除
```

#### main.jsx

```
SpeedInsights、Analytics 组件删除
如：
import { Analytics } from "@vercel/analytics/react";   删除
<Analytics />   组件删除
```

#### LandingPage.jsx

```
discord、twitter[Tweet]、shortenNumber函数
import axios from "axios";
import { languages } from "../i18n/i18n";


const [stats, setStats] = useState({ stars: 18000, forks: 1200 });

    const fetchStats = async () => {
      await axios
        .get("https://api.github-star-counter.workers.dev/user/StarForAll")
        .then((res) => setStats(res.data));
    };
fetchStats();

<div className="flex justify-center items-center gap-28 md:block">整个组件
<div className="px-40 mt-6 md:px-8">【Tweets】整个组件
<div className="bg-zinc-100 py-8 px-32 md:px-8"> 【Reach out to us】父 div
<div className="text-center text-sm py-3">
        &copy; 2024 <strong>drawDB</strong> - All right reserved.
      </div>
Supported by 相关
Templates 相关
```

> 以上内容都是需要删除的

#### ControlPanel.jsx

```
bug-report，socials.discord、save_as_template 去除

```

#### Navbar.jsx

```
templates、twitter、discord 去除
```

#### BugReport.jsx

```
保持一个空文件
```

#### NotFound.jsx

```
email、discord、create-diagram 删除
p标签 to create a relationship 删除
```

#### index.html

```
所有的 https://drawdb.app 去除 直接使用本项目中的资源路径
twitter 相关删除

head中补充脚本：
<script>
    // 获取当前页面的完整 URL
    const currentUrl = window.location.href;

    // 动态设置 og:url
    document.querySelector('meta[property="og:url"]').setAttribute('content', currentUrl);
</script>
```

#### README.md

```
dev标签<div align="center" style="margin-bottom:12px;">  删除

## Getting Started 二级标题下补充内容
> <https://github.com/settings/tokens/new>页创建一个名为VITE_GITHUB_ACCESS_TOKEN的token，勾选create  gists  
> 将创建的token记住，在最后run前，将token复制到.env文件中，如：不同的run需要不同的.env文件

补充
### docker run with existing nginx
```bash
#1.构建目录
npm ci  && npm run build
#2.将生成的dist挂载到已有的nginx的html目录下
cp -r dist /new/docker/nginx/html/drawdb
#3.生成ssl和配置drawdb的conf

#重启nginx容器
docker stop nginx-container && docker compose -f /new/docker/nginx/docker-compose.yml up -d
docker
```

### sync.yml内容

> 当不需要fork上游项目到自己仓库时，该脚本就不需要使用   
> 在使用的情况下，需要补充 UPSTREAM_REPO_SECRET 和 EMAIL_USERNAME 、EMAIL_PASSWORD 、 TO_EMAIL_USERNAME 等信息   
> 其中 UPSTREAM_REPO_SECRET
> 自带，后续三个Email参数值需要在`https://github.com/StarForAll/drawdb/settings/secrets/actions`中添加

```
name: Sync Branches from Upstream

on:
  schedule:
    - cron: '0 6 * * 1'
  workflow_dispatch:

jobs:
  sync-branches:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        branch: [ main ]  # 同步多个分支
      fail-fast: false  # 可以同步多个分支，即使有失败也继续执行下一分支同步操作
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
      cancel-in-progress: true  # 取消正在进行的相同工作流
    steps:
      - name: Checkout fork repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整的提交历史

      - name: Sync branch
        id: sync_branch
        uses: aormsby/Fork-Sync-With-Upstream-action@v3.4.1
        with:
          upstream_sync_repo: drawdb-io/drawdb
          upstream_sync_branch: ${{ matrix.branch }}  # 上游分支
          target_sync_branch: ${{ matrix.branch }}  # 目标分支
          upstream_repo_access_token: ${{ secrets.UPSTREAM_REPO_SECRET }}
          target_repo_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Log sync status
        run: |
          echo "Sync status for ${{ matrix.branch }}: ${{ steps.sync_branch.outcome }}"

      - name: Send email notification on sync failure
        if: failure() || steps.sync_branch.outcome != 'success'
        uses: dawidd6/action-send-mail@v4
        with:
          server_address: smtp.qiye.aliyun.com
          server_port: 465
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          from: "GitHub qwerty-learner sync"
          subject: "GitHub Actions: Branch Sync Failed"
          body: |
            The following branches failed to sync:
            - ${{ matrix.branch }}: ${{ steps.sync_branch.outcome }}
            Error details:
            ```
            ${{ steps.sync_branch.outputs.error || 'No error details available' }}
            ```
          to: ${{ secrets.TO_EMAIL_USERNAME }}

```
