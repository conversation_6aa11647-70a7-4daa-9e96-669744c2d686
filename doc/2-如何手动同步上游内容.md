### 需要手动同步的原因

> 由于本项目在fork之后,经过了大量修改,自动同步操作会失败,所以就需要手动同步上游内容.

### 同步操作步骤

#### 添加上游仓库[单次]

```angular2html
git remote add upstream https://github.com/xxx/xxx.git
# 本项目的上游是 https://github.com/drawdb-io/drawdb.git
```

#### 同步上游仓库[每次]

```angular2html
# 切换到需要同步的分支
git checkout main
# 备份当前分支
git branch backup/main-$(date +%Y%m%d%H%M) # unix
git branch backup/main-%DATE:~0,4%%DATE:~5,2%%DATE:~8,2% # windows
# 获取远程仓库的分支
git fetch upstream
# 合并到当前分支
git merge upstream/main --allow-unrelated-histories
#打开ide  根据实际情况解决解决冲突 --- 目前需要根据文档 1-从官方drawdb修改内容.md 进行修改
```

#### 测试同步结果

> 由于不同项目的运行方式不同,在进行完成代码同步和冲突解决之后,需要运行代码,解决可能存在的问题和修复需要修正的地方.

```angular2html
# 运行测试本项目
# 1. 将目录切换到项目根目录
npm install
npm run dev
# 打开对应的浏览器，访问项目的地址
# 点击各处并测试功能是否正常，使用效果是否符合预期 --- 如有问题，手动修复
```
