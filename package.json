{"name": "client-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@dbml/core": "^3.13.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@douyinfe/semi-ui": "^2.77.1", "@lexical/react": "^0.12.5", "@monaco-editor/react": "^4.7.0", "@vercel/analytics": "^1.2.2", "@vercel/speed-insights": "^1.2.0", "axios": "^1.8.2", "classnames": "^2.5.1", "dexie": "^3.2.4", "dexie-react-hooks": "^1.1.7", "file-saver": "^2.0.5", "framer-motion": "^10.18.0", "html-to-image": "^1.11.11", "i18next": "^23.11.4", "i18next-browser-languagedetector": "^8.0.0", "jsonschema": "^1.4.1", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lexical": "^0.12.5", "nanoid": "^5.1.5", "node-sql-parser": "^5.3.11", "oracle-sql-parser": "^0.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.4.1", "react-i18next": "^14.1.1", "react-router-dom": "^6.21.0", "react-tweet": "^3.2.1", "url": "^0.11.1", "usehooks-ts": "^3.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.14", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.3.4", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "3.2.5", "tailwindcss": "^4.0.14", "vite": "^6.3.4"}, "overrides": {"follow-redirects": "^1.15.4"}}