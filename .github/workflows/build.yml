name: Build

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
    types: [ opened, synchronize, reopened ]

jobs:
  build:

    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: --max-old-space-size=4096
    strategy:
      matrix:
        node-version: [ 18.x, 20.x ]

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - run: npm install
      #      - name: Run eslint
      #        run: npm run lint
      - name: Run vite build
        run: npm run build
