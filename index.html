<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <link href="/favicon.ico" rel="icon"/>

    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="#000000" name="theme-color"/>
    <meta
            content="Online database entity-realtionship diagram editor, data modeler, and SQL generator. Design, visualize, and export scripts without an account and completely free of charge."
            name="description"
    />

    <meta content="index,follow,max-image-preview:large" name="robots"/>

    <meta content="website" property="og:type"/>
    <meta content="/" property="og:url"/>
    <meta
            content="drawDB | Online database diagram editor and SQL generator"
            property="og:title"
    />
    <meta
            content="Online database entity-realtionship diagram editor, data modeler, and SQL generator. Design, visualize, and export scripts without an account and completely free of charge."
            property="og:description"
    />
    <meta content="/hero_ss.png" property="og:image"/>

    <link href="/favicon.ico" rel="apple-touch-icon"/>
    <link
            crossorigin="anonymous"
            href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"
            rel="stylesheet"
    />

    <link
            crossorigin="anonymous"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
            integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
            referrerpolicy="no-referrer"
            rel="stylesheet"
    />

    <title>drawDB | Online database diagram editor and SQL generator</title>
    <script>
        // 获取当前页面的完整 URL
        const currentUrl = window.location.href;
        // 动态设置 og:url
        document.querySelector('meta[property="og:url"]').setAttribute('content', currentUrl);
    </script>
</head>
<body theme-mode="light">
<noscript>You need to enable JavaScript to run this app.</noscript>
<div id="root"></div>
<script src="/src/main.jsx" type="module"></script>
</body>
</html>
